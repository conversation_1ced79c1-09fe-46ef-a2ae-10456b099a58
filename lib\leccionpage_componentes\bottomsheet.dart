import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

Future<void> showBottomSheet(BuildContext context, String linkName,
    Map<String, String> diccionarioCache) async {
  var definition = 'No se encontró ninguna definición';

  if (diccionarioCache.containsKey(linkName)) {
    definition = diccionarioCache[linkName]!;
  } else {
    definition = 'No se encontró ninguna definición';
  }

  if (context.mounted) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(20),
          decoration: const BoxDecoration(
            color: Color.fromARGB(255, 61, 60, 60),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.0),
              topRight: Radius.circular(20.0),
            ),
          ),
          child: Wrap(
            alignment: WrapAlignment.center,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    toBeginningOfSentenceCase(linkName) ?? '',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.white,
                    ),
                  ),
                  const Divider(color: Colors.white),
                  Text(
                    definition,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
