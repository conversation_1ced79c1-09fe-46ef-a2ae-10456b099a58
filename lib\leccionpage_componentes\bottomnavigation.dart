import 'package:flutter/material.dart';

class BottomNavigation extends StatefulWidget {
  final GlobalKey<ScaffoldState> scaffoldKey;
  final Function(String) onSearch; // Callback para la búsqueda
  final ValueNotifier<int> matchCountNotifier; // Notificador de coincidencias
  final ValueNotifier<int>
      currentMatchNotifier; // Notificador de coincidencia actual
  final VoidCallback
      onPreviousMatch; // Callback para ir a la coincidencia anterior
  final VoidCallback
      onNextMatch; // Callback para ir a la siguiente coincidencia

  const BottomNavigation({
    super.key,
    required this.scaffoldKey,
    required this.onSearch, // Callback para la búsqueda
    required this.matchCountNotifier, // Notificador de coincidencias
    required this.currentMatchNotifier, // Notificador de coincidencia actual
    required this.onPreviousMatch, // Callback para ir a la coincidencia anterior
    required this.onNextMatch, // Callback para ir a la siguiente coincidencia
  });

  @override
  // ignore: library_private_types_in_public_api
  _BottomNavigationState createState() => _BottomNavigationState();
}

final iconButtonStyle = IconButton.styleFrom(
  padding: EdgeInsets.zero,
  minimumSize: const Size(24, 24),
  iconSize: 20.0,
);

class _BottomNavigationState extends State<BottomNavigation>
    with SingleTickerProviderStateMixin {
  bool isSearchBarVisible = false;
  late AnimationController controller;
  late Animation<Offset> offset;
  final TextEditingController _textEditingController = TextEditingController();
  final FocusNode _focusNode = FocusNode(); // FocusNode para el TextField

  @override
  void initState() {
    super.initState();
    controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 250));
    offset = Tween<Offset>(begin: const Offset(0.0, 1.0), end: Offset.zero)
        .animate(controller);

    // Escuchar cambios en el TextEditingController
    _textEditingController.addListener(() {
      if (isSearchBarVisible) {
        widget.onSearch(_textEditingController.text);
      }
    });
  }

  @override
  void dispose() {
    controller.dispose();
    _textEditingController.dispose();
    _focusNode.dispose(); // Dispose del FocusNode
    super.dispose();
  }

  void showSearchBar() {
    setState(() {
      isSearchBarVisible = true;
    });
    controller.forward().then((_) {
      _focusNode
          .requestFocus(); // Solicitar el foco cuando se muestre la barra de búsqueda
    });
  }

  void hideSearchBar() {
    controller.reverse().then((_) {
      setState(() {
        isSearchBarVisible = false;
        _textEditingController.clear(); // Limpiar el campo de texto
        widget.onSearch(
            ''); // Llamar a la función de búsqueda con una cadena vacía
      });
    });
  }

  bool isSearchBarCurrentlyVisible() {
    return isSearchBarVisible;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Barra de búsqueda
        AnimatedContainer(
          duration: const Duration(milliseconds: 250),
          height: isSearchBarVisible ? 40 : 0, // Altura fija de 40
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            decoration: BoxDecoration(
              color: const Color(0xFF2D3748),
              borderRadius: BorderRadius.circular(4.0),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _textEditingController,
                    focusNode: _focusNode,
                    style: const TextStyle(color: Color(0xFFCBD5E0)),
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      isDense: true,
                    ),
                  ),
                ),
                const SizedBox(width: 8.0),
                ValueListenableBuilder<int>(
                  valueListenable: widget.matchCountNotifier,
                  builder: (context, matchCount, child) {
                    return ValueListenableBuilder<int>(
                      valueListenable: widget.currentMatchNotifier,
                      builder: (context, currentMatch, child) {
                        return Text(
                          '$currentMatch of $matchCount',
                          style: const TextStyle(color: Color(0xFF718096)),
                        );
                      },
                    );
                  },
                ),
                const SizedBox(width: 8.0),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.arrow_upward,
                          color: Color(0xFFCBD5E0)),
                      onPressed: widget.onPreviousMatch,
                      style: iconButtonStyle,
                    ),
                    IconButton(
                      icon: const Icon(Icons.arrow_downward,
                          color: Color(0xFFCBD5E0)),
                      onPressed: widget.onNextMatch,
                      style: iconButtonStyle,
                    ),
                    IconButton(
                      icon: const Icon(Icons.close, color: Color(0xFFCBD5E0)),
                      onPressed: hideSearchBar,
                      style: iconButtonStyle,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        // BottomNavigationBar
        Container(
          color: const Color.fromARGB(255, 9, 145, 152),
          height: 40.0,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: <Widget>[
              GestureDetector(
                onTap: () {
                  if (widget.scaffoldKey.currentState!.isDrawerOpen) {
                    Navigator.of(context).pop();
                  } else {
                    widget.scaffoldKey.currentState!.openDrawer();
                  }
                },
                child: const Icon(Icons.menu_book, size: 20.0),
              ),
              GestureDetector(
                onTap: () {
                  showSearchBar();
                },
                child: const Icon(Icons.search, size: 20.0),
              ),
              GestureDetector(
                onTap: () {},
                child: const Icon(Icons.settings, size: 20.0),
              ),
              GestureDetector(
                onTap: () {},
                child: const Icon(Icons.person, size: 20.0),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
