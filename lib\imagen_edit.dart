import 'dart:io';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:intl/intl.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';

class TrianglePainter extends CustomPainter {
  final Color color;

  TrianglePainter(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path()
      ..moveTo(size.width / 2, 0)
      ..lineTo(size.width, size.height)
      ..lineTo(0, size.height)
      ..close();

    canvas.drawPath(path, paint);

    final borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    canvas.drawPath(path, borderPaint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class ImagenEdit extends StatefulWidget {
  final String imagePath;
  final int leccionId;
  final int? imageId; // Para edición de imagen existente
  final Map<String, dynamic>?
      existingData; // Datos existentes de leccion_imagenes

  const ImagenEdit({
    super.key,
    required this.imagePath,
    required this.leccionId,
    this.imageId,
    this.existingData,
  });

  @override
  State<ImagenEdit> createState() => _ImagenEditState();
}

class _ImagenEditState extends State<ImagenEdit> {
  late File imageFile;
  double imageWidth = 0;
  double imageHeight = 0;

  // Controlador para la descripción general de la imagen
  final TextEditingController descripcionGeneralController =
      TextEditingController();

  // Controlador para la descripción de los puntos
  final TextEditingController puntoDescripcionController =
      TextEditingController();

  List<MarkerPoint> markers = [];
  GlobalKey imageKey = GlobalKey();

  double markerSize = 16.0;
  Color markerColor = Colors.red;
  MarkerShape markerShape = MarkerShape.circle;

  Offset? mousePosition;

  @override
  void initState() {
    super.initState();
    imageFile = File(widget.imagePath);
    _loadImageDimensions();
    _loadExistingData();
  }

  Future<void> _loadExistingData() async {
    if (widget.existingData != null) {
      debugPrint('📂 CARGANDO DATOS EXISTENTES');
      debugPrint('🆔 ImageID: ${widget.imageId}');
      final data = widget.existingData!;

      // Cargar descripción existente
      descripcionGeneralController.text = data['descripcion_especifica'] ?? '';
      debugPrint(
          '📝 Descripción cargada: "${data['descripcion_especifica'] ?? 'Sin descripción'}"');

      // Cargar marcadores existentes
      final List<dynamic> markersData = data['markers_data'] ?? [];
      debugPrint('🎯 Marcadores encontrados: ${markersData.length}');
      if (markersData.isNotEmpty) {
        setState(() {
          markers = markersData.map<MarkerPoint>((markerData) {
            return MarkerPoint(
              position:
                  Offset(markerData['x_position'], markerData['y_position']),
              description: markerData['description'],
              size: markerData['size'].toDouble(),
              color: Color(markerData['color']),
              shape: MarkerShape.values[markerData['shape']],
            );
          }).toList();
        });
        debugPrint('✅ Marcadores cargados exitosamente');
      }
    } else {
      debugPrint('🆕 MODO CREACIÓN - No hay datos existentes');
    }
  }

  Future<void> _loadImageDimensions() async {
    final decodedImage =
        await decodeImageFromList(await imageFile.readAsBytes());
    setState(() {
      imageWidth = decodedImage.width.toDouble();
      imageHeight = decodedImage.height.toDouble();
    });
  }

  void _addMarker(TapDownDetails details) {
    final RenderBox? imageBox =
        imageKey.currentContext?.findRenderObject() as RenderBox?;
    if (imageBox == null) return;

    final imageSize = imageBox.size;
    final imagePosition = imageBox.localToGlobal(Offset.zero);
    final localPosition = details.globalPosition - imagePosition;

    // Calculate the scale factor between the displayed image size and the actual image size
    final scaleX = imageWidth / imageSize.width;
    final scaleY = imageHeight / imageSize.height;

    if (localPosition.dx >= 0 &&
        localPosition.dx <= imageSize.width &&
        localPosition.dy >= 0 &&
        localPosition.dy <= imageSize.height) {
      final actualX = localPosition.dx * scaleX;
      final actualY = localPosition.dy * scaleY;

      setState(() {
        markers.add(
          MarkerPoint(
            position: Offset(actualX, actualY),
            description: puntoDescripcionController.text.isEmpty
                ? 'Punto ${markers.length + 1}'
                : puntoDescripcionController.text,
            size: markerSize,
            color: markerColor,
            shape: markerShape,
          ),
        );
        puntoDescripcionController.clear(); // Limpiar solo el texto del punto
      });
    }
  }

  void showColorPicker() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Seleccionar Color'),
        content: SingleChildScrollView(
          child: ColorPicker(
            pickerColor: markerColor,
            onColorChanged: (color) {
              setState(() {
                markerColor = color; // Actualizar el color seleccionado
              });
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('Aceptar'),
          ),
        ],
      ),
    );
  }

  Widget getMarkerWidget(MarkerPoint marker, String text) {
    switch (marker.shape) {
      case MarkerShape.circle:
        return Container(
          width: marker.size,
          height: marker.size,
          decoration: BoxDecoration(
            color: marker.color,
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 2),
          ),
          child: Center(
            child: Text(
              text,
              style: TextStyle(
                color: Colors.white,
                fontSize: marker.size * 0.5,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
      case MarkerShape.square:
        return Container(
          width: marker.size,
          height: marker.size,
          decoration: BoxDecoration(
            color: marker.color,
            border: Border.all(color: Colors.white, width: 2),
          ),
          child: Center(
            child: Text(
              text,
              style: TextStyle(
                color: Colors.white,
                fontSize: marker.size * 0.5,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
      case MarkerShape.triangle:
        return SizedBox(
          width: marker.size,
          height: marker.size,
          child: Stack(
            children: [
              CustomPaint(
                painter: TrianglePainter(marker.color),
                size: Size(marker.size, marker.size),
              ),
              Center(
                child: Text(
                  text,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: marker.size * 0.5,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        );
    }
  }

  IconData getShapeIcon(MarkerShape shape) {
    switch (shape) {
      case MarkerShape.circle:
        return Icons.circle_outlined;
      case MarkerShape.square:
        return Icons.square_outlined;
      case MarkerShape.triangle:
        return Icons.change_history_outlined;
    }
  }

  void _showMarkerDialog(MarkerPoint marker) {
    final int currentIndex = markers.indexOf(marker);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Punto ${currentIndex + 1}'),
        content: StatefulBuilder(
          builder: (BuildContext context, StateSetter setDialogState) {
            return SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      IconButton(
                        icon: Icon(Icons.arrow_upward),
                        onPressed: currentIndex > 0
                            ? () {
                                setState(() {
                                  final temp = markers[currentIndex - 1];
                                  markers[currentIndex - 1] = marker;
                                  markers[currentIndex] = temp;
                                  Navigator.pop(context);
                                  _showMarkerDialog(marker);
                                });
                              }
                            : null,
                      ),
                      Text('Orden: ${currentIndex + 1}'),
                      IconButton(
                        icon: Icon(Icons.arrow_downward),
                        onPressed: currentIndex < markers.length - 1
                            ? () {
                                setState(() {
                                  final temp = markers[currentIndex + 1];
                                  markers[currentIndex + 1] = marker;
                                  markers[currentIndex] = temp;
                                  Navigator.pop(context);
                                  _showMarkerDialog(marker);
                                });
                              }
                            : null,
                      ),
                    ],
                  ),
                  TextField(
                    controller: TextEditingController(text: marker.description),
                    onChanged: (value) {
                      setState(() {
                        marker.description = value;
                      });
                    },
                    decoration: const InputDecoration(labelText: 'Descripción'),
                  ),
                  const SizedBox(height: 10),
                  Row(
                    children: [
                      const Text('Color: '),
                      IconButton(
                        icon: Icon(Icons.color_lens, color: marker.color),
                        onPressed: () {
                          showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: const Text('Seleccionar Color'),
                              content: SingleChildScrollView(
                                child: ColorPicker(
                                  pickerColor: marker.color,
                                  onColorChanged: (color) {
                                    setState(() {
                                      marker.color = color;
                                    });
                                    setDialogState(() {});
                                  },
                                ),
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.pop(context),
                                  child: const Text('Aceptar'),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      const Text('Forma: '),
                      DropdownButton<MarkerShape>(
                        value: marker.shape,
                        items: MarkerShape.values
                            .map((shape) => DropdownMenuItem(
                                  value: shape,
                                  child: Icon(getShapeIcon(shape)),
                                ))
                            .toList(),
                        onChanged: (shape) {
                          setState(() {
                            marker.shape = shape!;
                          });
                          setDialogState(() {});
                        },
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      const Text('Tamaño: '),
                      Expanded(
                        child: Slider(
                          value: marker.size,
                          min: 8.0,
                          max: 32.0,
                          onChanged: (value) {
                            setState(() {
                              marker.size = value;
                            });
                            setDialogState(() {});
                          },
                        ),
                      ),
                    ],
                  ),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        markers.remove(marker);
                      });
                      Navigator.pop(context);
                    },
                    style:
                        ElevatedButton.styleFrom(backgroundColor: Colors.red),
                    child: const Text('Eliminar Punto'),
                  ),
                ],
              ),
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cerrar'),
          ),
        ],
      ),
    );
  }

  Future<void> saveImageData() async {
    final user = Supabase.instance.client.auth.currentUser;
    if (user == null || !mounted) return;

    final now = DateTime.now();
    final formattedDate = DateFormat('yyyy-MM-dd').format(now);
    final imageName = widget.imagePath.split(RegExp(r'[\\/]+')).last;

    // Copiar la imagen a la carpeta de destino
    await _copyImageToDestination(imageName);

    try {
      int imageId;

      if (widget.imageId != null) {
        // Modo edición: usar el imageId existente
        imageId = widget.imageId!;
        debugPrint(
            '🔄 MODO EDICIÓN - Actualizando imagen existente con ID: $imageId');

        // Actualizar la fecha de edición en la tabla imagenes
        await Supabase.instance.client
            .from('imagenes')
            .update({'edited_at': formattedDate}).eq('imagen_id', imageId);
        debugPrint('✅ Fecha de edición actualizada en tabla imagenes');
      } else {
        // Modo creación: insertar nueva imagen
        debugPrint('🆕 MODO CREACIÓN - Insertando nueva imagen: $imageName');
        final imageResponse = await Supabase.instance.client
            .from('imagenes')
            .insert({
              'imagen_nombre': imageName,
              'created_at': formattedDate,
              'edited_at': formattedDate,
            })
            .select()
            .maybeSingle();

        if (imageResponse == null) {
          throw Exception('Error al insertar imagen en la base de datos');
        }

        imageId = imageResponse['imagen_id'];
        debugPrint('✅ Nueva imagen insertada con ID: $imageId');

        // Insertar en la tabla 'imagen_creador' solo para nuevas imágenes
        await Supabase.instance.client.from('imagen_creador').insert({
          'imagen_id': imageId,
          'creador_id': user.id,
        });
        debugPrint('✅ Relación imagen_creador insertada');
      }

      // Convertir los marcadores a un formato serializable
      final markersJson = markers
          .map((marker) => {
                'x_position': marker.position.dx,
                'y_position': marker.position.dy,
                'description': marker.description,
                'size': marker.size,
                'color': marker.color
                    .toARGB32(), // Usar toARGB32 para obtener el valor ARGB
                'shape': marker.shape.index,
                'image_width': imageWidth,
                'image_height': imageHeight,
              })
          .toList();

      if (widget.existingData != null) {
        // Modo edición: actualizar leccion_imagenes existente
        debugPrint(
            '🔄 ACTUALIZANDO leccion_imagenes - ImageID: $imageId, LeccionID: ${widget.leccionId}');
        debugPrint('📝 Marcadores a actualizar: ${markersJson.length}');
        await Supabase.instance.client
            .from('leccion_imagenes')
            .update({
              'descripcion_especifica': descripcionGeneralController.text,
              'markers_data': markersJson,
            })
            .eq('imagen_id', imageId)
            .eq('leccion_id', widget.leccionId);
        debugPrint('✅ leccion_imagenes ACTUALIZADO exitosamente');
      } else {
        // Modo creación: insertar nueva entrada en leccion_imagenes
        debugPrint(
            '🆕 INSERTANDO nueva entrada en leccion_imagenes - ImageID: $imageId, LeccionID: ${widget.leccionId}');
        debugPrint('📝 Marcadores a insertar: ${markersJson.length}');
        await Supabase.instance.client.from('leccion_imagenes').insert({
          'leccion_id': widget.leccionId,
          'imagen_id': imageId,
          'descripcion_especifica': descripcionGeneralController.text,
          'markers_data': markersJson,
        });
        debugPrint('✅ leccion_imagenes INSERTADO exitosamente');
      }

      if (mounted) {
        Navigator.pop(context, imageName);
      }
    } catch (e) {
      debugPrint('Error saving image: $e');
    }
  }

  Future<void> _copyImageToDestination(String imageName) async {
    try {
      // Crear la carpeta de destino si no existe
      final destinationDir = Directory(
          'C:/Users/<USER>/AppData/Roaming/com.example/medicappp/images');
      if (!await destinationDir.exists()) {
        await destinationDir.create(recursive: true);
      }

      // Ruta de destino para la imagen
      final destinationPath = '${destinationDir.path}/$imageName';
      final destinationFile = File(destinationPath);

      // Copiar la imagen solo si no existe ya en el destino
      if (!await destinationFile.exists()) {
        await imageFile.copy(destinationPath);
        debugPrint('Imagen copiada a: $destinationPath');
      } else {
        debugPrint('La imagen ya existe en: $destinationPath');
      }
    } catch (e) {
      debugPrint('Error al copiar la imagen: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool isEditMode = widget.existingData != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(isEditMode ? 'Editar Imagen' : 'Marcar Puntos en Imagen'),
      ),
      body: Column(
        children: [
          Expanded(
            flex: 8, // 80% of the available height
            child: LayoutBuilder(
              builder: (BuildContext context, BoxConstraints constraints) {
                return Center(
                    child: MouseRegion(
                  onHover: (event) {
                    final RenderBox? imageBox = imageKey.currentContext
                        ?.findRenderObject() as RenderBox?;
                    if (imageBox != null) {
                      final imageSize = imageBox.size;
                      final imagePosition = imageBox.localToGlobal(Offset.zero);
                      final localPosition = event.position - imagePosition;

                      if (localPosition.dx >= 0 &&
                          localPosition.dx <= imageSize.width &&
                          localPosition.dy >= 0 &&
                          localPosition.dy <= imageSize.height) {
                        setState(() {
                          mousePosition = localPosition;
                        });
                      } else {
                        setState(() {
                          mousePosition = null;
                        });
                      }
                    }
                  },
                  child: GestureDetector(
                    onTapDown: _addMarker,
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: SingleChildScrollView(
                        scrollDirection: Axis.vertical,
                        child: Stack(
                          key: imageKey,
                          children: [
                            Image.file(
                              imageFile,
                            ),
                            ...markers.map((marker) {
                              return Positioned(
                                left: marker.position.dx - (marker.size / 2),
                                top: marker.position.dy - (marker.size / 2),
                                child: Draggable(
                                  feedback: getMarkerWidget(
                                      marker, '${markers.indexOf(marker) + 1}'),
                                  childWhenDragging: Container(),
                                  onDragEnd: (details) {
                                    setState(() {
                                      final RenderBox? box = imageKey
                                          .currentContext
                                          ?.findRenderObject() as RenderBox?;
                                      if (box != null) {
                                        final localPosition =
                                            box.globalToLocal(details.offset);
                                        final Size imageSize = box.size;
                                        // Add marker size offset compensation
                                        final double x =
                                            (localPosition.dx + marker.size / 2)
                                                .clamp(0, imageSize.width);
                                        final double y =
                                            (localPosition.dy + marker.size / 2)
                                                .clamp(0, imageSize.height);
                                        marker.position = Offset(x, y);
                                      }
                                    });
                                  },
                                  child: GestureDetector(
                                    onTap: () => _showMarkerDialog(marker),
                                    child: getMarkerWidget(marker,
                                        '${markers.indexOf(marker) + 1}'),
                                  ),
                                ),
                              );
                            }),
                          ],
                        ),
                      ),
                    ),
                  ),
                ));
              },
            ),
          ),
          if (mousePosition != null)
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                'Coordenadas del mouse: (${mousePosition!.dx.toStringAsFixed(2)}, ${mousePosition!.dy.toStringAsFixed(2)})\n'
                'Tamaño real de la imagen: ${imageWidth.toStringAsFixed(2)} x ${imageHeight.toStringAsFixed(2)}',
                textAlign: TextAlign.center,
              ),
            ),
          Expanded(
            flex: 2, // 20% of the available height
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: TextField(
                        controller:
                            descripcionGeneralController, // Usar el nuevo controlador
                        maxLines: null,
                        keyboardType: TextInputType.multiline,
                        decoration: const InputDecoration(
                          labelText: 'Descripción de la imagen',
                          border: OutlineInputBorder(),
                          alignLabelWithHint: true,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    height: 50,
                    child: ElevatedButton.icon(
                      onPressed: saveImageData,
                      icon: Icon(isEditMode ? Icons.update : Icons.save),
                      label: Text(
                        isEditMode ? 'Actualizar Imagen' : 'Guardar Imagen',
                        style: const TextStyle(
                            fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor:
                            isEditMode ? Colors.orange : Colors.green,
                        foregroundColor: Colors.white,
                        elevation: 3,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class MarkerPoint {
  Offset position;
  String description;
  double size;
  Color color;
  MarkerShape shape;

  MarkerPoint({
    required this.position,
    required this.description,
    required this.size,
    required this.color,
    required this.shape,
  });
}

enum MarkerShape { circle, square, triangle }
