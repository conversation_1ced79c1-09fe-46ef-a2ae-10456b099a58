# Agent Guide for appmanager

## Commands
- **Test**: `flutter test` (all tests), `flutter test test/widget_test.dart` (single test)
- **Build**: `flutter build apk` (Android), `flutter build windows` (Windows)
- **Run**: `flutter run` (debug), `flutter run --release` (release)
- **Lint**: `flutter analyze` (static analysis using flutter_lints)

## Architecture
- **Flutter app** with Supabase backend integration for authentication and data
- **Database**: Supabase (cloud) + local SQLite via sqflite_ffi for desktop platforms
- **Structure**: `lib/` contains main code, organized by feature (leccion, usuario, galeria)
- **Components**: Separate folders for page components (`leccionpage_componentes/`, `homepage_componentes/`)
- **Key files**: `main.dart` (entry), `database_helper.dart` (Supabase integration), `leccion_edit.dart` (lesson editor)

## Code Style
- **Imports**: Standard library first, Flutter packages, third-party packages, local imports last
- **Naming**: snake_case for files, camelCase for variables/functions, PascalCase for classes
- **Linting**: Uses `flutter_lints` package with default rules
- **Comments**: Use `// ignore_for_file:` for file-level lint suppression
- **Async**: Extensive use of async/await, proper Future handling with error checking
- **State**: StatefulWidget pattern, controllers for text inputs, StreamController for data streams
- **Environment**: Uses `.env` files for secrets (supabasekeys.env)
