import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class UsuarioEditPage extends StatefulWidget {
  const UsuarioEditPage({super.key});

  @override
  UsuarioEditPageState createState() => UsuarioEditPageState();
}

class UsuarioEditPageState extends State<UsuarioEditPage> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _usernameController;
  late TextEditingController _fullNameController;
  late TextEditingController _emailController;
  late final SupabaseClient _supabaseClient;
  List<dynamic> _roles = [];
  int? _selectedRoleId;

  @override
  void initState() {
    super.initState();
    _supabaseClient = Supabase.instance.client;
    _usernameController = TextEditingController();
    _fullNameController = TextEditingController();
    _emailController = TextEditingController();
    _fetchRoles();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final usuario = ModalRoute.of(context)!.settings.arguments as Map;

    // Inicializar controladores
    _usernameController.text = usuario['username'] ?? '';
    _fullNameController.text = usuario['full_name'] ?? '';
    _emailController.text = usuario['email'] ?? '';

    // Solo inicializar _selectedRoleId si es null
    if (_selectedRoleId == null) {
      setState(() {
        _selectedRoleId = usuario['role'];
      });
    }
  }

  Future<void> _fetchRoles() async {
    try {
      final response =
          await _supabaseClient.from('roles').select('id, role_name');

      if (mounted) {
        setState(() {
          _roles = response;
        });
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error al obtener roles: $error')),
        );
      }
    }
  }

  Future<void> _updateUser() async {
    if (_formKey.currentState!.validate()) {
      final usuario = ModalRoute.of(context)!.settings.arguments as Map;
      try {
        final updates = {
          'username': _usernameController.text,
          'full_name': _fullNameController.text,
          'email': _emailController.text,
          'role': _selectedRoleId, // Usar el valor actualizado
        };

        final response = await _supabaseClient
            .from('profiles')
            .update(updates)
            .eq('user_id', usuario['user_id'])
            .select();

        if (response.isNotEmpty) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Rol actualizado exitosamente')),
            );
            Navigator.pop(context, true);
          }
        }
      } catch (error) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error al actualizar usuario: $error')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Editar Usuario'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              TextFormField(
                controller: _usernameController,
                decoration: const InputDecoration(labelText: 'Username'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Por favor ingrese un username';
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: _fullNameController,
                decoration: const InputDecoration(labelText: 'Nombre Completo'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Por favor ingrese el nombre completo';
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(labelText: 'Email'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Por favor ingrese un email';
                  }
                  return null;
                },
              ),
              DropdownButtonFormField<int>(
                value: _selectedRoleId,
                decoration: const InputDecoration(labelText: 'Rol'),
                items: _roles.map<DropdownMenuItem<int>>((role) {
                  return DropdownMenuItem<int>(
                    value: role['id'],
                    child: Text('${role['role_name']} (ID: ${role['id']})'),
                  );
                }).toList(),
                onChanged: (int? newValue) {
                  setState(() {
                    _selectedRoleId = newValue;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'Por favor seleccione un rol';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: _updateUser,
                child: const Text('Guardar Cambios'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _fullNameController.dispose();
    _emailController.dispose();
    super.dispose();
  }
}
