import 'package:flutter/material.dart';
import 'dart:math';

class AnimatedWaveText extends StatefulWidget {
  const AnimatedWaveText({super.key});

  @override
  State<AnimatedWaveText> createState() => _AnimatedWaveTextState();
}

class _AnimatedWaveTextState extends State<AnimatedWaveText>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller =
        AnimationController(vsync: this, duration: const Duration(seconds: 3))
          ..repeat();
    _animation = Tween<double>(begin: 0, end: 2 * pi).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return ShaderMask(
            blendMode: BlendMode.srcIn,
            shaderCallback: (Rect bounds) {
              return LinearGradient(
                colors: [
                  Colors.grey.shade800,
                  Colors.grey.shade400,
                  Colors.grey.shade800,
                ],
                stops: const [0.0, 0.5, 1.0],
                begin: Alignment(-1 + _animation.value / pi, 0),
                end: Alignment(1 + _animation.value / pi, 0),
                tileMode: TileMode.mirror,
              ).createShader(bounds);
            },
            child: const Center(
              child: Padding(
                padding: EdgeInsets.only(
                    bottom: 20), // Ajusta el valor según necesites
                child: Text(
                  'Appmanager',
                  style: TextStyle(
                    fontSize: 80,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ));
      },
    );
  }
}
