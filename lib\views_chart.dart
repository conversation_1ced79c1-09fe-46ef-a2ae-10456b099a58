import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fl_chart/fl_chart.dart';

class ViewsChartPage extends StatefulWidget {
  final int? leccionId;

  const ViewsChartPage({super.key, this.leccionId});

  @override
  ViewsChartPageState createState() => ViewsChartPageState();
}

class ViewsChartPageState extends State<ViewsChartPage> {
  List<Map<String, dynamic>> _data = [];
  bool _loading = true;
  bool showAvg = false;
  int? _selectedMonth; // Mes seleccionado (1-12), null para todos
  int daysInMonth(int year, int month) {
    var beginningNextMonth =
        (month < 12) ? DateTime(year, month + 1, 1) : DateTime(year + 1, 1, 1);
    return beginningNextMonth.subtract(const Duration(days: 1)).day;
  }

  final List<String> monthNames = [
    '<PERSON>ero',
    'Febrero',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>',
    'Agosto',
    'Septiembre',
    'Octubre',
    'Noviembre',
    'Diciembre'
  ];
  List<Color> gradientColors = [
    Colors.cyan,
    Colors.blue,
  ];

  @override
  void initState() {
    super.initState();
    fetchChartData();
  }

  Future<void> fetchChartData() async {
    final supabaseClient = Supabase.instance.client;

    final params = <String, dynamic>{};
    if (widget.leccionId != null) {
      params['leccion_id'] = widget.leccionId;
    }

    final response = await supabaseClient.rpc(
      'contar_visualizaciones_por_leccion',
      params:
          widget.leccionId != null ? {'leccion_id': widget.leccionId} : null,
    );

    if (response == null) {
      setState(() {
        _loading = false;
      });
      return;
    }

    List<dynamic> jsonData;
    try {
      if (response is String) {
        jsonData = jsonDecode(response);
      } else if (response is List) {
        jsonData = response;
      } else {
        jsonData = [];
      }
    } catch (e) {
      jsonData = [];
    }

    setState(() {
      _data = jsonData.cast<Map<String, dynamic>>();
      _loading = false;
    });
  }

  int? _selectedYear; // Año seleccionado, null para todos

  List<Map<String, dynamic>> get _filteredData {
    return _data.where((item) {
      final date = DateTime.parse(item['fecha']);
      if (_selectedYear != null && date.year != _selectedYear) {
        return false;
      }
      if (_selectedMonth != null && date.month != _selectedMonth) {
        return false;
      }
      return true;
    }).toList();
  }

  Widget bottomTitleWidgets(double value, TitleMeta meta) {
    const style = TextStyle(
      fontWeight: FontWeight.bold,
      fontSize: 10,
    );
    Widget text;
    int index = value.toInt() - 1; // Restar 1 para índice correcto
    if (index < 0 || index >= _sortedDates.length) {
      text = const Text('');
    } else {
      text = Text(_sortedDates[index], style: style);
    }
    return SideTitleWidget(
      meta: meta,
      child: text,
    );
  }

  Widget leftTitleWidgets(double value, TitleMeta meta) {
    const style = TextStyle(
      fontWeight: FontWeight.bold,
      fontSize: 12,
    );
    if (value % 5 == 0) {
      return Text(value.toInt().toString(), style: style);
    }
    return Container();
  }

  late List<String> _sortedDates;
  late List<FlSpot> _spots;

  Map<String, int> totalesPorFecha = {};

  void prepareChartData() {
    totalesPorFecha.clear();
    _spots = [];

    if (_selectedYear == null && _selectedMonth == null) {
      // Agrupar por año y mes para todos los datos
      Map<String, int> totalsPerYearMonth = {};
      for (var item in _data) {
        DateTime date = DateTime.parse(item['fecha']);
        String yearMonth =
            '${date.year}-${date.month.toString().padLeft(2, '0')}';
        int total = (item['total_visualizaciones'] as num).toInt();

        totalsPerYearMonth[yearMonth] =
            (totalsPerYearMonth[yearMonth] ?? 0) + total;

        totalesPorFecha[yearMonth] = (totalesPorFecha[yearMonth] ?? 0) + total;
      }

      List<String> sortedYearMonths = totalsPerYearMonth.keys.toList()..sort();
      _sortedDates = sortedYearMonths;

      _spots = [];
      for (int i = 0; i < sortedYearMonths.length; i++) {
        _spots.add(FlSpot(i.toDouble() + 1,
            totalsPerYearMonth[sortedYearMonths[i]]!.toDouble()));
      }
    } else if (_selectedYear != null && _selectedMonth == null) {
      // Agrupar por mes dentro del año seleccionado, asegurando 12 meses
      Map<int, int> totalsPerMonth = {};
      for (int month = 1; month <= 12; month++) {
        totalsPerMonth[month] = 0;
      }
      for (var item in _filteredData) {
        DateTime date = DateTime.parse(item['fecha']);
        int month = date.month;
        int total = (item['total_visualizaciones'] as num).toInt();

        totalsPerMonth[month] = (totalsPerMonth[month] ?? 0) + total;

        String monthKey = '${date.year}-${month.toString().padLeft(2, '0')}';
        totalesPorFecha[monthKey] = (totalesPorFecha[monthKey] ?? 0) + total;
      }

      List<int> sortedMonths = totalsPerMonth.keys.toList()..sort();
      _sortedDates = sortedMonths.map((m) => monthNames[m - 1]).toList();

      _spots = [];
      for (int month = 1; month <= 12; month++) {
        _spots.add(FlSpot(month.toDouble(), totalsPerMonth[month]!.toDouble()));
      }
    } else if (_selectedYear != null && _selectedMonth != null) {
      // Agrupar por día dentro del mes y año seleccionados
      int daysInMonth =
          DateUtils.getDaysInMonth(_selectedYear!, _selectedMonth!);
      Map<int, int> totalsPerDay = {};
      for (int day = 1; day <= daysInMonth; day++) {
        totalsPerDay[day] = 0;
      }
      for (var item in _filteredData) {
        DateTime date = DateTime.parse(item['fecha']);
        int day = date.day;
        int total = (item['total_visualizaciones'] as num).toInt();
        totalsPerDay[day] = (totalsPerDay[day] ?? 0) + total;
        totalesPorFecha[date.toIso8601String().split('T')[0]] =
            (totalesPorFecha[date.toIso8601String().split('T')[0]] ?? 0) +
                total;
      }

      List<int> sortedDays = totalsPerDay.keys.toList()..sort();
      _sortedDates = sortedDays.map((d) => d.toString()).toList();

      _spots = [];
      for (int day = 1; day <= daysInMonth; day++) {
        _spots.add(FlSpot(day.toDouble(), totalsPerDay[day]!.toDouble()));
      }
    }
  }

  LineChartData mainData() {
    prepareChartData();

    int maxX = _sortedDates.length;

    return LineChartData(
      minX: 1,
      maxX: maxX.toDouble(),
      lineTouchData: LineTouchData(
        touchTooltipData: LineTouchTooltipData(
          getTooltipItems: (touchedSpots) {
            return touchedSpots.map((touchedSpot) {
              final xValue = touchedSpot.x.toInt();
              String label = '';
              int totalVisualizaciones = 0;
              int visualizacionesUnicas = 0;

              if (_selectedYear == null && _selectedMonth == null) {
                // Caso: Sin filtros (agrupado por año y mes)
                if (xValue - 1 < 0 || xValue - 1 >= _sortedDates.length) {
                  label = '';
                } else {
                  label = _sortedDates[xValue - 1];
                }

                final dataForYearMonth = _data.where((item) {
                  DateTime date = DateTime.parse(item['fecha']);
                  String yearMonth =
                      '${date.year}-${date.month.toString().padLeft(2, '0')}';
                  return yearMonth == _sortedDates[xValue - 1];
                }).toList();

                for (var d in dataForYearMonth) {
                  totalVisualizaciones +=
                      (d['total_visualizaciones'] as num).toInt();
                  visualizacionesUnicas +=
                      (d['visualizaciones_unicas'] as num).toInt();
                }
              } else if (_selectedYear != null && _selectedMonth == null) {
                // Caso: Filtro por año
                if (xValue < 1 || xValue > 12) {
                  label = '';
                } else {
                  label = monthNames[xValue - 1];
                }

                final dataForMonth = _filteredData.where((item) {
                  DateTime date = DateTime.parse(item['fecha']);
                  return date.month == xValue;
                }).toList();

                for (var d in dataForMonth) {
                  totalVisualizaciones +=
                      (d['total_visualizaciones'] as num).toInt();
                  visualizacionesUnicas +=
                      (d['visualizaciones_unicas'] as num).toInt();
                }
              } else if (_selectedYear != null && _selectedMonth != null) {
                // Caso: Filtro por año y mes
                if (xValue - 1 < 0 || xValue - 1 >= _sortedDates.length) {
                  label = '';
                } else {
                  label = 'Día ${_sortedDates[xValue - 1]}';
                }

                final dataForDay = _filteredData.where((item) {
                  DateTime date = DateTime.parse(item['fecha']);
                  return date.day == int.parse(_sortedDates[xValue - 1]);
                }).toList();

                for (var d in dataForDay) {
                  totalVisualizaciones +=
                      (d['total_visualizaciones'] as num).toInt();
                  visualizacionesUnicas +=
                      (d['visualizaciones_unicas'] as num).toInt();
                }
              }

              return LineTooltipItem(
                '$label\nTotal: $totalVisualizaciones\nÚnicas: $visualizacionesUnicas',
                const TextStyle(color: Colors.black),
              );
            }).toList();
          },
        ),
      ),
      gridData: FlGridData(
        show: true,
        drawVerticalLine: true,
        horizontalInterval: 1,
        verticalInterval: 1,
        getDrawingHorizontalLine: (value) {
          return const FlLine(
            color: Colors.grey,
            strokeWidth: 1,
          );
        },
        getDrawingVerticalLine: (value) {
          return const FlLine(
            color: Colors.grey,
            strokeWidth: 1,
          );
        },
      ),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false, reservedSize: 100),
        ),
        topTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 30,
            interval: 1,
            getTitlesWidget: bottomTitleWidgets,
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            interval: 5,
            getTitlesWidget: leftTitleWidgets,
            reservedSize: 40,
          ),
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: Colors.black),
      ),
      minY: 0,
      maxY: _spots.isNotEmpty
          ? (_spots.map((e) => e.y).reduce((a, b) => a > b ? a : b) + 5)
          : 10,
      lineBarsData: [
        LineChartBarData(
          spots: _spots,
          isCurved: true,
          gradient: LinearGradient(
            colors: gradientColors,
          ),
          barWidth: 2,
          isStrokeCapRound: true,
          dotData: FlDotData(show: true),
          belowBarData: BarAreaData(
            show: true,
            gradient: LinearGradient(
              colors: gradientColors
                  // ignore: deprecated_member_use
                  .map((color) => color.withOpacity(0.3))
                  .toList(),
            ),
          ),
        ),
      ],
    );
  }

  LineChartData avgData() {
    prepareChartData();

    int maxX = 12;
    if (_selectedYear != null && _selectedMonth == null) {
      maxX = 12;
    } else if (_selectedYear == null && _selectedMonth == null) {
      maxX = _sortedDates.length;
    } else if (_selectedYear != null && _selectedMonth != null) {
      maxX = _sortedDates.length;
    }

    return LineChartData(
      minX: 1,
      maxX: maxX.toDouble(),
      lineTouchData: LineTouchData(
        touchTooltipData: LineTouchTooltipData(
          getTooltipItems: (touchedSpots) {
            return touchedSpots.map((touchedSpot) {
              final xValue = touchedSpot.x.toInt();
              String label = '';
              int totalVisualizaciones = 0;
              int visualizacionesUnicas = 0;

              if (_selectedYear != null && _selectedMonth == null) {
                if (xValue < 1 || xValue > 12) {
                  label = '';
                } else {
                  label =
                      monthNames[xValue - 1]; // Corregido: restar 1 al índice
                }
                final dataForMonth = _filteredData.where((item) {
                  DateTime date = DateTime.parse(item['fecha']);
                  return date.month == xValue;
                }).toList();

                for (var d in dataForMonth) {
                  totalVisualizaciones +=
                      (d['total_visualizaciones'] as num).toInt();
                  visualizacionesUnicas +=
                      (d['visualizaciones_unicas'] as num).toInt();
                }
              } else if (_selectedYear != null && _selectedMonth == null) {
                if (xValue < 1 || xValue > 12) {
                  label = '';
                } else {
                  label = monthNames[xValue - 1];
                }
                final dataForMonth = _filteredData.where((item) {
                  DateTime date = DateTime.parse(item['fecha']);
                  return date.month == xValue;
                }).toList();

                for (var d in dataForMonth) {
                  totalVisualizaciones +=
                      (d['total_visualizaciones'] as num).toInt();
                  visualizacionesUnicas +=
                      (d['visualizaciones_unicas'] as num).toInt();
                }
              } else if (_selectedYear != null && _selectedMonth != null) {
                if (xValue - 1 < 0 || xValue - 1 >= _sortedDates.length) {
                  label = '';
                } else {
                  label = 'Día ${_sortedDates[xValue - 1]}';
                }
                final dataForDay = _filteredData.where((item) {
                  DateTime date = DateTime.parse(item['fecha']);
                  return date.day == int.parse(_sortedDates[xValue - 1]);
                }).toList();

                for (var d in dataForDay) {
                  totalVisualizaciones +=
                      (d['total_visualizaciones'] as num).toInt();
                  visualizacionesUnicas +=
                      (d['visualizaciones_unicas'] as num).toInt();
                }
              }

              return LineTooltipItem(
                '$label\nTotal: $totalVisualizaciones\nÚnicas: $visualizacionesUnicas',
                const TextStyle(color: Colors.black),
              );
            }).toList();
          },
        ),
      ),
      gridData: FlGridData(
        show: true,
        drawHorizontalLine: true,
        verticalInterval: 1,
        horizontalInterval: 1,
        getDrawingVerticalLine: (value) {
          return const FlLine(
            color: Colors.grey,
            strokeWidth: 1,
          );
        },
        getDrawingHorizontalLine: (value) {
          return const FlLine(
            color: Colors.grey,
            strokeWidth: 1,
          );
        },
      ),
      titlesData: FlTitlesData(
        show: true,
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 30,
            getTitlesWidget: bottomTitleWidgets,
            interval: 1,
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            getTitlesWidget: leftTitleWidgets,
            reservedSize: 40,
            interval: 1,
          ),
        ),
        topTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        rightTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false, reservedSize: 100),
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: Colors.black),
      ),
      minY: 0,
      maxY: _spots.isNotEmpty
          ? (_spots.map((e) => e.y).reduce((a, b) => a > b ? a : b) + 5)
          : 10,
      lineBarsData: [
        LineChartBarData(
          spots: _spots.isNotEmpty
              ? _spots.map((e) => FlSpot(e.x, e.y * 0.7)).toList()
              : [],
          isCurved: true,
          gradient: LinearGradient(
            colors: [
              gradientColors[0],
              gradientColors[1],
            ],
          ),
          barWidth: 2,
          isStrokeCapRound: true,
          dotData: FlDotData(show: false),
          belowBarData: BarAreaData(show: false),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return Scaffold(
        appBar: AppBar(title: const Text('Visualizaciones por Día y Hora')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_data.isEmpty) {
      return Scaffold(
        appBar: AppBar(title: const Text('Visualizaciones por Día y Hora')),
        body: const Center(child: Text('No hay datos para mostrar')),
      );
    }
    List<int> years = _data
        .map((item) => DateTime.parse(item['fecha']).year)
        .toSet()
        .toList()
      ..sort();
    return Scaffold(
      appBar: AppBar(
        title: const Text('Visualizaciones por Día y Hora'),
        actions: [
          IconButton(
            icon: Icon(showAvg ? Icons.show_chart : Icons.bar_chart),
            onPressed: () {
              setState(() {
                showAvg = !showAvg;
              });
            },
            tooltip: 'Toggle Chart View',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Selector de mes y botón limpiar filtro
            Row(
              children: [
                DropdownButton<int>(
                  hint: const Text('Selecciona mes'),
                  value: _selectedMonth,
                  items: List.generate(12, (index) {
                    final month = index + 1;
                    return DropdownMenuItem(
                      value: month,
                      child: Text(monthNames[index]),
                    );
                  }),
                  onChanged: (value) {
                    setState(() {
                      _selectedMonth = value;
                    });
                  },
                ),
                DropdownButton<int>(
                  hint: const Text('Selecciona año'),
                  value: _selectedYear,
                  items: years.map((year) {
                    return DropdownMenuItem(
                      value: year,
                      child: Text(year.toString()),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedYear = value;
                      _selectedMonth = null; // Limpiar mes al cambiar año
                    });
                  },
                ),
                const SizedBox(width: 16),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _selectedYear = null;
                      _selectedMonth = null;
                    });
                  },
                  child: const Text('Mostrar todos'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Center(
                child: SizedBox(
                  width: MediaQuery.of(context).size.width *
                      0.9, // 90% del ancho de pantalla
                  child: LineChart(
                    showAvg ? avgData() : mainData(),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Total de visitas por día:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            ...totalesPorFecha.entries
                .map((entry) => Text('${entry.key}: ${entry.value}')),
          ],
        ),
      ),
    );
  }
}
