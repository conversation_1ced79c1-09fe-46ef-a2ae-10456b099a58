import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class CeldaData {
  String contenido = '';
  Color colorFondo = Colors.grey[600]!;
  TextEditingController? controller;

  void initController() {
    controller ??= TextEditingController(text: contenido);
  }

  void dispose() {
    controller?.dispose();
  }
}

class CuadroEditor extends StatefulWidget {
  final int? cuadroId;
  final int? leccionId; // Para crear la relación automáticamente

  const CuadroEditor({
    super.key,
    this.cuadroId,
    this.leccionId,
  });

  @override
  State<CuadroEditor> createState() => _CuadroEditorState();
}

class _CuadroEditorState extends State<CuadroEditor> {
  final TextEditingController _tituloController = TextEditingController();
  int _columnas = 1;
  int _filas = 1;
  bool _isLoading = false;
  bool _isEditing = false;
  Color _colorCuadroGlobal = Colors.grey[600]!; // Color global por defecto
  int? _celdaSeleccionadaFila;
  int? _celdaSeleccionadaColumna;
  int? _columnaSeleccionada; // Para selección de columna completa
  String? _contenidoCopiado; // Para funcionalidad de copiar/pegar
  bool _isPreviewPanelVisible = false;
  double _editorFlex = 3.0;

  // Estados para controlar la visibilidad de los selectores con hover
  int? _columnaHover; // Columna sobre la que está el mouse
  int? _filaHover; // Fila sobre la que está el mouse
  Timer? _hoverTimer; // Timer para controlar el delay del hover

  // Matriz para almacenar el contenido de cada celda
  List<List<CeldaData>> _matriz = [];

  // Historial para comandos de teclado (undo/redo)
  final List<Map<String, dynamic>> _historial = [];
  int _historialIndex = -1;

  // Anchos de columnas personalizados
  late ValueNotifier<List<double>> _anchosColumnasNotifier;

  List<double> get _anchosColumnas => _anchosColumnasNotifier.value;
  set _anchosColumnas(List<double> value) =>
      _anchosColumnasNotifier.value = value;

  // Clipboard para copy/paste
  String? _clipboardContent;

  // Timer para auto-guardar en historial
  Timer? _autoSaveTimer;

  double _fontSize = 14.0;

  @override
  void initState() {
    super.initState();
    _anchosColumnasNotifier = ValueNotifier<List<double>>([]);
    _inicializarMatriz();
    // Seleccionar automáticamente la primera celda
    _celdaSeleccionadaFila = 0;
    _celdaSeleccionadaColumna = 0;
    if (widget.cuadroId != null) {
      _cargarCuadro();
    }
  }

  String _buildContentString(List<dynamic> items, {int level = 0}) {
    StringBuffer buffer = StringBuffer();
    String indent = '  ' * level;

    for (var item in items) {
      if (item is String) {
        buffer.write('$indent- $item\n');
      } else if (item is Map<String, dynamic>) {
        final title = item['titulo'] ?? '';
        final content = item['contenido'] as List<dynamic>? ?? [];

        if (title.isNotEmpty) {
          // Verificar si el título ya tiene una viñeta
          final bool alreadyHasBullet = title.startsWith('•') ||
              title.startsWith('◦') ||
              title.startsWith('-');

          if (alreadyHasBullet) {
            // Si ya tiene viñeta, escribirlo como está pero con formato de viñeta
            buffer.write('$indent- ${title.substring(2).trim()}\n');
          } else {
            // Si no tiene viñeta, es un título normal
            buffer.write('$indent$title\n');
          }
        }
        buffer.write(_buildContentString(content, level: level + 1));
      }
    }
    return buffer.toString();
  }

  Future<void> _cargarCuadro() async {
    if (widget.cuadroId == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await Supabase.instance.client
          .from('cuadros')
          .select('*')
          .eq('cuadro_id', widget.cuadroId!)
          .single();

      _tituloController.text = response['titulo'] ?? '';

      if (response['json_data'] != null) {
        Map<String, dynamic> contenidoData;
        try {
          contenidoData = response['json_data'] is String
              ? jsonDecode(response['json_data'] as String)
                  as Map<String, dynamic>
              : response['json_data'] as Map<String, dynamic>;
        } catch (e) {
          contenidoData = {};
        }
        _filas = contenidoData['filas'] ?? 2;
        _columnas = contenidoData['columnas'] ?? 2;

        if (contenidoData['color'] != null) {
          _colorCuadroGlobal = Color(contenidoData['color'] as int);
        }

        if (contenidoData['fontSize'] != null) {
          _fontSize = (contenidoData['fontSize'] as num).toDouble();
        }

        _inicializarMatriz();

        if (contenidoData['anchosColumnas'] != null) {
          _anchosColumnas = List<double>.from(contenidoData['anchosColumnas']);
        }
        if (contenidoData['columnasData'] != null) {
          final columnasData = contenidoData['columnasData'] as List<dynamic>;
          _anchosColumnas = columnasData.map((col) {
            final ancho = col['ancho'];
            if (ancho is int) {
              return ancho.toDouble();
            } else if (ancho is double) {
              return ancho;
            } else {
              return 200.0;
            }
          }).toList();
        } else {
          if (contenidoData['anchosColumnas'] != null) {
            final anchos = List<double>.from(contenidoData['anchosColumnas']);
            final columnasData = List.generate(anchos.length, (index) {
              return {
                'columna': index,
                'ancho': anchos[index],
              };
            });
            contenidoData['columnasData'] = columnasData;
          } else {
            final columnasData = List.generate(_columnas, (index) {
              return {
                'columna': index,
                'ancho': 200.0,
              };
            });
            contenidoData['columnasData'] = columnasData;
          }
          await Supabase.instance.client.from('cuadros').update(
              {'json_data': contenidoData}).eq('cuadro_id', widget.cuadroId!);
        }

        _guardarEstadoEnHistorial();

        if (contenidoData['celdas'] != null) {
          final celdasData = contenidoData['celdas'] as List;
          for (var celdaInfo in celdasData) {
            final celdaData = celdaInfo as Map<String, dynamic>;
            final fila = celdaData['fila'] as int;
            final columna = celdaData['columna'] as int;
            final contenido = celdaData['contenido'];
            final colorFondo = celdaData['colorFondo']; // CARGAR COLOR DE CELDA

            if (fila < _filas && columna < _columnas) {
              String contenidoTexto;
              if (contenido is List) {
                contenidoTexto = _buildContentString(contenido);
              } else if (contenido is String) {
                contenidoTexto = contenido;
              } else {
                contenidoTexto = '';
              }
              _matriz[fila][columna].contenido = contenidoTexto.trim();
              _matriz[fila][columna].initController();
              _matriz[fila][columna].controller!.text =
                  _matriz[fila][columna].contenido;

              // APLICAR COLOR DE CELDA SI EXISTE
              if (colorFondo != null) {
                _matriz[fila][columna].colorFondo = Color(colorFondo as int);
              }
            }
          }
        }
      }

      setState(() {
        _isEditing = true;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error al cargar cuadro: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _hoverTimer?.cancel();
    _autoSaveTimer?.cancel();
    _tituloController.dispose();
    // Limpiar todos los controladores de las celdas
    for (var fila in _matriz) {
      for (var celda in fila) {
        celda.dispose();
      }
    }
    super.dispose();
  }

  void _inicializarMatriz() {
    _matriz = List.generate(_filas, (fila) {
      return List.generate(_columnas, (columna) {
        final celda = CeldaData();
        celda.colorFondo = _colorCuadroGlobal;
        celda.contenido = '';
        celda.initController();
        return celda;
      });
    });

    // Inicializar anchos de columnas con valor por defecto
    _anchosColumnasNotifier.value = List.generate(_columnas, (index) => 200.0);
  }

  // Funciones para comandos de teclado
  void _guardarEstadoEnHistorial() {
    // Limpiar historial futuro si estamos en el medio
    if (_historialIndex < _historial.length - 1) {
      _historial.removeRange(_historialIndex + 1, _historial.length);
    }

    // Agregar estado actual
    _historial.add({
      'matriz': _matriz
          .map((fila) => fila
              .map((celda) => {
                    'contenido': celda.contenido,
                    'colorFondo': celda.colorFondo.toARGB32(),
                  })
              .toList())
          .toList(),
      'filas': _filas,
      'columnas': _columnas,
      'anchosColumnas': List.from(_anchosColumnas),
    });

    _historialIndex = _historial.length - 1;

    // Limitar historial a 50 estados
    if (_historial.length > 50) {
      _historial.removeAt(0);
      _historialIndex--;
    }
  }

  void _deshacerCambio() {
    if (_historialIndex > 0) {
      _historialIndex--;
      _restaurarEstado(_historial[_historialIndex]);
    }
  }

  void _rehacerCambio() {
    if (_historialIndex < _historial.length - 1) {
      _historialIndex++;
      _restaurarEstado(_historial[_historialIndex]);
    }
  }

  void _restaurarEstado(Map<String, dynamic> estado) {
    setState(() {
      _filas = estado['filas'];
      _columnas = estado['columnas'];
      _anchosColumnas = List<double>.from(estado['anchosColumnas']);

      // Limpiar matriz actual
      for (var fila in _matriz) {
        for (var celda in fila) {
          celda.dispose();
        }
      }

      // Restaurar matriz
      _matriz = List.generate(_filas, (fila) {
        return List.generate(_columnas, (columna) {
          final celda = CeldaData();
          final celdaData = estado['matriz'][fila][columna];
          celda.contenido = celdaData['contenido'];
          celda.colorFondo = Color(celdaData['colorFondo']);
          celda.initController();
          celda.controller!.text = celda.contenido;
          return celda;
        });
      });
    });
  }

  void _copiarCeldaSeleccionada() {
    if (_celdaSeleccionadaFila != null && _celdaSeleccionadaColumna != null) {
      _clipboardContent = _matriz[_celdaSeleccionadaFila!]
              [_celdaSeleccionadaColumna!]
          .contenido;
    }
  }

  void _pegarCeldaSeleccionada() {
    if (_clipboardContent != null &&
        _celdaSeleccionadaFila != null &&
        _celdaSeleccionadaColumna != null) {
      _guardarEstadoEnHistorial();
      setState(() {
        _matriz[_celdaSeleccionadaFila!][_celdaSeleccionadaColumna!].contenido =
            _clipboardContent!;
        _matriz[_celdaSeleccionadaFila!][_celdaSeleccionadaColumna!]
            .controller!
            .text = _clipboardContent!;
      });
    }
  }

  List<Widget> _buildPreviewContenidoJerarquico(List<dynamic> items,
      {int level = 0}) {
    List<Widget> widgets = [];
    final double indent = 16.0 * level;

    for (var item in items) {
      if (item is String) {
        if (item.trim().isEmpty) continue;

        const bullets = ['•', '◦', '-'];
        final bullet =
            bullets[level < bullets.length ? level : bullets.length - 1];

        widgets.add(
          Padding(
            padding: EdgeInsets.only(left: indent, bottom: 4.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  bullet,
                  style: TextStyle(
                    fontSize: _fontSize,
                    color: Colors.black87,
                  ),
                ),
                SizedBox(width: 4.0),
                Expanded(
                  child: Text(
                    item,
                    style: TextStyle(
                      fontSize: _fontSize,
                      color: Colors.black87,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      } else if (item is Map<String, dynamic>) {
        final titulo = item['titulo'] as String? ?? '';
        final contenido = item['contenido'] as List<dynamic>? ?? [];

        if (titulo.isNotEmpty) {
          // Verificar si el título ya tiene una viñeta
          final bool alreadyHasBullet = titulo.startsWith('•') ||
              titulo.startsWith('◦') ||
              titulo.startsWith('-');

          widgets.add(
            Padding(
              padding: EdgeInsets.only(left: indent, bottom: 8.0, top: 4.0),
              child: alreadyHasBullet
                  ? Text(
                      titulo,
                      style: TextStyle(
                        fontSize: _fontSize,
                        fontWeight: FontWeight.normal,
                        color: Colors.black87,
                      ),
                    )
                  : Text(
                      titulo,
                      style: TextStyle(
                        fontSize: _fontSize,
                        fontWeight:
                            FontWeight.bold, // Títulos sin viñeta en negrita
                        color: Colors.black87,
                      ),
                    ),
            ),
          );
        }
        widgets.addAll(
            _buildPreviewContenidoJerarquico(contenido, level: level + 1));
      }
    }
    return widgets;
  }

  List<dynamic> _parseContentToJson(String content) {
    final lines = content.split('\n');
    if (lines.isEmpty) return [];

    final List<dynamic> root = [];
    final stack = <List<dynamic>>[root];
    final indentStack = [-1];

    int getIndent(String line) {
      int i = 0;
      while (i < line.length && line[i] == ' ') {
        i++;
      }
      return i;
    }

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];
      if (line.trim().isEmpty) continue;

      final indent = getIndent(line);
      final text = line.trim();
      final isBullet = text.startsWith('- ');

      while (indent <= indentStack.last) {
        stack.removeLast();
        indentStack.removeLast();
      }

      if (!isBullet) {
        // Line is a title (parent)
        final newNode = {
          'titulo': text,
          'contenido': <dynamic>[],
        };
        stack.last.add(newNode);
        stack.add(newNode['contenido'] as List<dynamic>);
        indentStack.add(indent);
      } else {
        // Line is a bullet point
        final contentText = text.substring(2).trim();

        // Check next line indent to decide if this bullet has children
        bool hasChildren = false;
        if (i + 1 < lines.length) {
          final nextLine = lines[i + 1];
          final nextIndent = getIndent(nextLine);
          if (nextIndent > indent) {
            hasChildren = true;
          }
        }

        if (hasChildren) {
          // This bullet is a parent node BUT KEEP IT AS A BULLET
          final newNode = {
            'titulo': '• $contentText', // Mantener la viñeta explícitamente
            'contenido': <dynamic>[],
          };
          stack.last.add(newNode);
          stack.add(newNode['contenido'] as List<dynamic>);
          indentStack.add(indent);
        } else {
          // This bullet is a leaf node (string)
          if (contentText.isNotEmpty) {
            stack.last.add(contentText);
          }
        }
      }
    }

    return root;
  }

  Future<void> _guardarCuadro() async {
    if (_tituloController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('El título es obligatorio')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Convertir matriz a formato de celdas original
      List<Map<String, dynamic>> celdas = [];

      for (int fila = 0; fila < _filas; fila++) {
        for (int columna = 0; columna < _columnas; columna++) {
          final celda = _matriz[fila][columna];
          final contenidoJson = _parseContentToJson(celda.contenido);

          celdas.add({
            'fila': fila,
            'columna': columna,
            'titulo':
                '', // Vacío porque ahora hay múltiples títulos en contenido
            'contenido': contenidoJson,
            'colorFondo': celda.colorFondo.toARGB32(), // AGREGAR COLOR DE CELDA
          });
        }
      }

      final columnasData = List.generate(_columnas, (index) {
        return {
          'columna': index,
          'ancho': _anchosColumnas[index],
        };
      });

      final contenidoData = {
        'filas': _filas,
        'columnas': _columnas,
        'color': _colorCuadroGlobal.toARGB32(), // Guardar color como int
        'fontSize': _fontSize, // Guardar tamaño de letra
        'columnasData':
            columnasData, // Guardar anchos por columna como atributo por columna
        'celdas': celdas,
      };

      if (_isEditing && widget.cuadroId != null) {
        await Supabase.instance.client.from('cuadros').update({
          'titulo': _tituloController.text.trim(),
          'json_data': contenidoData,
        }).eq('cuadro_id', widget.cuadroId!);
      } else {
        final response = await Supabase.instance.client
            .from('cuadros')
            .insert({
              'titulo': _tituloController.text.trim(),
              'json_data': contenidoData,
            })
            .select()
            .single();

        if (widget.leccionId != null) {
          final cuadroId = response['cuadro_id'];
          await Supabase.instance.client.from('leccion_cuadros').insert({
            'leccion_id': widget.leccionId!,
            'cuadro_id': cuadroId is String ? int.parse(cuadroId) : cuadroId,
          });
        }
      }

      if (mounted) {
        Navigator.pop(
            context,
            widget.cuadroId?.toString() ??
                ''); // Return cuadroId string instead of bool
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error al guardar: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _eliminarFila(int index) {
    if (_filas > 1) {
      setState(() {
        _filas--;
        _matriz.removeAt(index);

        // Adjust selection to prevent RangeError
        if (_celdaSeleccionadaFila != null) {
          // If selection was after the deleted row, shift it up.
          if (_celdaSeleccionadaFila! > index) {
            _celdaSeleccionadaFila = _celdaSeleccionadaFila! - 1;
          }
          // After potential shifting, ensure it's within bounds.
          // This handles the case where the last row was deleted while selected.
          if (_celdaSeleccionadaFila! >= _filas) {
            _celdaSeleccionadaFila = _filas - 1;
          }
        }
      });
    }
  }

  void _eliminarColumna(int index) {
    if (_columnas > 1) {
      setState(() {
        _columnas--;
        for (int fila = 0; fila < _filas; fila++) {
          _matriz[fila].removeAt(index);
        }
        // Ajustar selecciones
        if (_celdaSeleccionadaColumna != null &&
            _celdaSeleccionadaColumna! >= index) {
          _celdaSeleccionadaColumna = _celdaSeleccionadaColumna! > index
              ? _celdaSeleccionadaColumna! - 1
              : null;
        }
        if (_columnaSeleccionada != null && _columnaSeleccionada! >= index) {
          _columnaSeleccionada =
              _columnaSeleccionada! > index ? _columnaSeleccionada! - 1 : null;
        }
      });
    }
  }

  // Funciones del menú contextual
  void _mostrarSelectorColorFila(int fila) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Seleccionar color para fila ${fila + 1}'),
        content: Wrap(
          children: [
            for (Color color in [
              Colors.white,
              Colors.red[100]!,
              Colors.blue[100]!,
              Colors.green[100]!,
              Colors.yellow[100]!,
              Colors.purple[100]!,
              Colors.orange[100]!,
              Colors.grey[100]!,
            ])
              GestureDetector(
                onTap: () {
                  setState(() {
                    for (int col = 0; col < _columnas; col++) {
                      _matriz[fila][col].colorFondo = color;
                    }
                  });
                  Navigator.pop(context);
                },
                child: Container(
                  width: 40,
                  height: 40,
                  margin: EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: color,
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _insertarFilaArriba(int index) {
    setState(() {
      _filas++;
      _matriz.insert(index, List.generate(_columnas, (columna) => CeldaData()));
      if (_celdaSeleccionadaFila != null && _celdaSeleccionadaFila! >= index) {
        _celdaSeleccionadaFila = _celdaSeleccionadaFila! + 1;
      }
    });
  }

  void _insertarFilaAbajo(int index) {
    setState(() {
      _filas++;
      _matriz.insert(
          index + 1, List.generate(_columnas, (columna) => CeldaData()));
    });
  }

  void _duplicarFila(int index) {
    setState(() {
      _filas++;
      List<CeldaData> filaDuplicada = [];
      for (var celda in _matriz[index]) {
        CeldaData nuevaCelda = CeldaData();
        nuevaCelda.colorFondo = celda.colorFondo;
        nuevaCelda.contenido = celda.contenido;
        nuevaCelda.initController();
        nuevaCelda.controller!.text = nuevaCelda.contenido;
        filaDuplicada.add(nuevaCelda);
      }
      _matriz.insert(index + 1, filaDuplicada);
    });
  }

  void _limpiarContenidoFila(int index) {
    setState(() {
      for (int col = 0; col < _columnas; col++) {
        _matriz[index][col].contenido = '';
        _matriz[index][col].controller?.text = '';
      }
    });
  }

  // Funciones del menú contextual de columna
  void _mostrarSelectorColorColumna(int columna) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Seleccionar color para columna ${columna + 1}'),
        content: Wrap(
          children: [
            for (Color color in [
              Colors.white,
              Colors.red[100]!,
              Colors.blue[100]!,
              Colors.green[100]!,
              Colors.yellow[100]!,
              Colors.purple[100]!,
              Colors.orange[100]!,
              Colors.grey[100]!,
            ])
              GestureDetector(
                onTap: () {
                  setState(() {
                    for (int fila = 0; fila < _filas; fila++) {
                      _matriz[fila][columna].colorFondo = color;
                    }
                  });
                  Navigator.pop(context);
                },
                child: Container(
                  width: 40,
                  height: 40,
                  margin: EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: color,
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _insertarColumnaIzquierda(int index) {
    _guardarEstadoEnHistorial();
    setState(() {
      _columnas++;
      for (int fila = 0; fila < _filas; fila++) {
        CeldaData celda = CeldaData();
        celda.colorFondo = _colorCuadroGlobal;
        celda.initController();
        _matriz[fila].insert(index, celda);
      }
      // Insertar ancho de columna por defecto
      _anchosColumnas.insert(index, 200.0);

      // Ajustar selección
      if (_celdaSeleccionadaColumna != null &&
          _celdaSeleccionadaColumna! >= index) {
        _celdaSeleccionadaColumna = _celdaSeleccionadaColumna! + 1;
      }
      if (_columnaSeleccionada != null && _columnaSeleccionada! >= index) {
        _columnaSeleccionada = _columnaSeleccionada! + 1;
      }
    });
  }

  void _insertarColumnaDerecha(int index) {
    _guardarEstadoEnHistorial();
    setState(() {
      _columnas++;
      for (int fila = 0; fila < _filas; fila++) {
        CeldaData celda = CeldaData();
        celda.colorFondo = _colorCuadroGlobal;
        celda.initController();
        _matriz[fila].insert(index + 1, celda);
      }
      // Insertar ancho de columna por defecto
      _anchosColumnas.insert(index + 1, 200.0);
    });
  }

  void _duplicarColumna(int index) {
    setState(() {
      _columnas++;
      for (int fila = 0; fila < _filas; fila++) {
        CeldaData celdaOriginal = _matriz[fila][index];
        CeldaData nuevaCelda = CeldaData();
        nuevaCelda.colorFondo = celdaOriginal.colorFondo;
        nuevaCelda.contenido = celdaOriginal.contenido;
        nuevaCelda.initController();
        nuevaCelda.controller!.text = nuevaCelda.contenido;
        _matriz[fila].insert(index + 1, nuevaCelda);
      }
    });
  }

  void _limpiarContenidoColumna(int index) {
    setState(() {
      for (int fila = 0; fila < _filas; fila++) {
        _matriz[fila][index].contenido = '';
        _matriz[fila][index].controller?.text = '';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return KeyboardListener(
      focusNode: FocusNode(),
      autofocus: true,
      onKeyEvent: (KeyEvent event) {
        if (event is KeyDownEvent) {
          final isCtrlPressed = HardwareKeyboard.instance.isControlPressed;
          final key = event.logicalKey;

          if (isCtrlPressed) {
            if (key == LogicalKeyboardKey.keyZ) {
              _deshacerCambio();
            } else if (key == LogicalKeyboardKey.keyY) {
              _rehacerCambio();
            } else if (key == LogicalKeyboardKey.keyC) {
              _copiarCeldaSeleccionada();
            } else if (key == LogicalKeyboardKey.keyV) {
              _pegarCeldaSeleccionada();
            }
          }
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(_isEditing ? 'Editar Cuadro' : 'Nuevo Cuadro'),
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _buildEditor(),
      ),
    );
  }

  Widget _buildEditor() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Campo título
          TextField(
            controller: _tituloController,
            decoration: const InputDecoration(
              labelText: 'Título del cuadro',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 8),

          // Barra de herramientas
          _buildToolbar(),
          const SizedBox(height: 8),

          // Selector de tamaño de fuente
          Row(
            children: [
              const Text('Tamaño de letra: '),
              DropdownButton<double>(
                value: _fontSize,
                items: [12, 14, 16, 18, 20, 24, 28, 32]
                    .map((size) => DropdownMenuItem<double>(
                          value: size.toDouble(),
                          child: Text(size.toString()),
                        ))
                    .toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _fontSize = value;
                    });
                  }
                },
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Editor de tabla y vista previa
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: _editorFlex.round(),
                  child: _buildTableEditor(),
                ),
                if (_isPreviewPanelVisible) ...[
                  GestureDetector(
                    onHorizontalDragUpdate: (details) {
                      setState(() {
                        final newFlex = _editorFlex + (details.delta.dx / 100);
                        _editorFlex = newFlex.clamp(1, 5);
                      });
                    },
                    child: const MouseRegion(
                      cursor: SystemMouseCursors.resizeColumn,
                      child: VerticalDivider(
                        width: 20,
                        thickness: 1,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: (6 - _editorFlex).round(),
                    child: _buildPreviewPanel(),
                  ),
                ],
              ],
            ),
          ),

          // Mostrar anchos de columnas en la parte inferior
          ValueListenableBuilder<List<double>>(
            valueListenable: _anchosColumnasNotifier,
            builder: (context, anchos, child) {
              return Container(
                height: 40,
                margin: const EdgeInsets.only(top: 16),
                padding: const EdgeInsets.symmetric(horizontal: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: List.generate(anchos.length, (index) {
                    return Container(
                      width: 60,
                      alignment: Alignment.center,
                      child: Text(
                        'Col ${index + 1}: ${anchos[index].toStringAsFixed(1)}',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                    );
                  }),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          IconButton(
            icon: Icon(
              _isPreviewPanelVisible ? Icons.visibility_off : Icons.visibility,
            ),
            onPressed: () {
              setState(() {
                _isPreviewPanelVisible = !_isPreviewPanelVisible;
              });
            },
            tooltip: 'Mostrar/Ocultar vista previa',
          ),
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 12.0),
              child: SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(strokeWidth: 3),
              ),
            )
          else
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _guardarCuadro,
              tooltip: 'Guardar cuadro',
            ),
        ],
      ),
    );
  }

  Widget _buildPreviewPanel() {
    // Get content of the selected cell
    String currentContent = '';
    if (_celdaSeleccionadaFila != null && _celdaSeleccionadaColumna != null) {
      currentContent = _matriz[_celdaSeleccionadaFila!]
              [_celdaSeleccionadaColumna!]
          .contenido;
    }

    // Parse and build widgets
    final jsonContent = _parseContentToJson(currentContent);
    final previewWidgets = _buildPreviewContenidoJerarquico(jsonContent);

    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Vista Previa',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
            const Divider(),
            if (previewWidgets.isNotEmpty)
              ...previewWidgets
            else
              const Text(
                'Escribe en una celda para ver la vista previa.',
                style: TextStyle(color: Colors.grey),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTableEditor() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: Container(
          margin: const EdgeInsets.only(
              top: 35, left: 35), // Espacio para selectores
          child: Stack(
            clipBehavior:
                Clip.none, // Permitir que los selectores salgan del Stack
            children: [
              // Tabla de datos
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  for (int fila = 0; fila < _filas; fila++)
                    IntrinsicHeight(
                      child: Stack(
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              for (int columna = 0;
                                  columna < _columnas;
                                  columna++)
                                _buildCelda(fila, columna),
                            ],
                          ),
                          // Botón flotante fijo en la primera línea de esta fila
                          _buildSelectorFilaIntegrado(fila),
                        ],
                      ),
                    ),
                ],
              ),
              // Selectores flotantes de columna
              for (int columna = 0; columna < _columnas; columna++)
                _buildSelectorColumnaFlotante(columna),
            ],
          ),
        ),
      ),
    );
  }

  // Funciones del menú contextual de fila (simplificada para selectores flotantes)
  void _mostrarMenuContextualFila(
      BuildContext context, Offset position, int fila) {
    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        position.dx,
        position.dy,
        position.dx + 1,
        position.dy + 1,
      ),
      items: [
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.palette, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Color'),
            ],
          ),
          onTap: () => _mostrarSelectorColorFila(fila),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.keyboard_arrow_up, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Insert Above'),
            ],
          ),
          onTap: () => _insertarFilaArriba(fila),
        ),
        PopupMenuItem(
          child: Row(
            children: [],
          ),
          onTap: () => _mostrarSelectorColorFila(fila),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.keyboard_arrow_up, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Insert Above'),
            ],
          ),
          onTap: () => _insertarFilaArriba(fila),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.keyboard_arrow_down,
                  size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Insert Below'),
            ],
          ),
          onTap: () => _insertarFilaAbajo(fila),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.content_copy, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Duplicate'),
            ],
          ),
          onTap: () => _duplicarFila(fila),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.clear, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Clear Contents'),
            ],
          ),
          onTap: () => _limpiarContenidoFila(fila),
        ),
        if (_filas > 1)
          PopupMenuItem(
            child: Row(
              children: [
                Icon(Icons.delete, size: 16, color: Colors.red[600]),
                SizedBox(width: 8),
                Text('Delete', style: TextStyle(color: Colors.red[600])),
              ],
            ),
            onTap: () => _eliminarFila(fila),
          ),
      ],
    );
  }

  // Mostrar menú contextual para operaciones de columna
  void _mostrarMenuContextualColumna(
      BuildContext context, Offset position, int columna) {
    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        position.dx,
        position.dy,
        position.dx + 1,
        position.dy + 1,
      ),
      items: [
        PopupMenuItem(
          enabled: false,
          child: SizedBox(
            width: 200,
            child: TextField(
              decoration: InputDecoration(
                hintText: 'Search actions...',
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              style: TextStyle(fontSize: 14),
            ),
          ),
        ),
        PopupMenuItem(
          enabled: false,
          child: Text('Table',
              style: TextStyle(
                  fontWeight: FontWeight.bold, color: Colors.grey[600])),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.palette, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Color'),
              Spacer(),
              Icon(Icons.chevron_right, size: 16, color: Colors.grey[400]),
            ],
          ),
          onTap: () => _mostrarSelectorColorColumna(columna),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.keyboard_arrow_left,
                  size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Insert Left'),
            ],
          ),
          onTap: () => _insertarColumnaIzquierda(columna),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.keyboard_arrow_right,
                  size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Insert Right'),
            ],
          ),
          onTap: () => _insertarColumnaDerecha(columna),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.content_copy, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Duplicate'),
              Spacer(),
              Text('Ctrl+D',
                  style: TextStyle(color: Colors.grey[400], fontSize: 12)),
            ],
          ),
          onTap: () => _duplicarColumna(columna),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.clear, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Clear Contents'),
            ],
          ),
          onTap: () => _limpiarContenidoColumna(columna),
        ),
        if (_columnas > 1)
          PopupMenuItem(
            child: Row(
              children: [
                Icon(Icons.delete, size: 16, color: Colors.red[600]),
                SizedBox(width: 8),
                Text('Delete', style: TextStyle(color: Colors.red[600])),
                Spacer(),
                Text('Del',
                    style: TextStyle(color: Colors.grey[400], fontSize: 12)),
              ],
            ),
            onTap: () => _eliminarColumna(columna),
          ),
      ],
    );
  }

  // Mostrar menú contextual para operaciones de celda individual
  void _mostrarMenuContextualCelda(
      BuildContext context, Offset position, int fila, int columna) {
    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        position.dx,
        position.dy,
        position.dx + 1,
        position.dy + 1,
      ),
      items: [
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.content_copy, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Copy'),
              Spacer(),
              Text('Ctrl+C',
                  style: TextStyle(color: Colors.grey[400], fontSize: 12)),
            ],
          ),
          onTap: () => _copiarCelda(fila, columna),
        ),
        if (_contenidoCopiado != null)
          PopupMenuItem(
            child: Row(
              children: [
                Icon(Icons.content_paste, size: 16, color: Colors.grey[600]),
                SizedBox(width: 8),
                Text('Paste'),
                Spacer(),
                Text('Ctrl+V',
                    style: TextStyle(color: Colors.grey[400], fontSize: 12)),
              ],
            ),
            onTap: () => _pegarCelda(fila, columna),
          ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.clear, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Clear Contents'),
            ],
          ),
          onTap: () => _limpiarCelda(fila, columna),
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.palette, size: 16, color: Colors.grey[600]),
              SizedBox(width: 8),
              Text('Color'),
              Spacer(),
              Icon(Icons.chevron_right, size: 16, color: Colors.grey[400]),
            ],
          ),
          onTap: () => _mostrarSelectorColorCelda(fila, columna),
        ),
      ],
    );
  }

  // Funciones para operaciones de celda individual
  void _copiarCelda(int fila, int columna) {
    final celda = _matriz[fila][columna];
    setState(() {
      _contenidoCopiado = celda.contenido;
    });
  }

  void _pegarCelda(int fila, int columna) {
    if (_contenidoCopiado != null) {
      setState(() {
        _matriz[fila][columna].contenido = _contenidoCopiado!;
        _matriz[fila][columna].controller?.text = _contenidoCopiado!;
      });
    }
  }

  void _limpiarCelda(int fila, int columna) {
    setState(() {
      _matriz[fila][columna].contenido = '';
      _matriz[fila][columna].controller?.text = '';
    });
  }

  void _mostrarSelectorColorCelda(int fila, int columna) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Seleccionar color para celda'),
        content: SizedBox(
          width: 300,
          child: Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              // Colores básicos
              Colors.white,
              Colors.grey[100]!,
              Colors.grey[300]!,
              Colors.grey[600]!,
              Colors.black,

              // Rojos
              Colors.red[50]!,
              Colors.red[100]!,
              Colors.red[300]!,
              Colors.red[600]!,
              Colors.red[900]!,

              // Azules
              Colors.blue[50]!,
              Colors.blue[100]!,
              Colors.blue[300]!,
              Colors.blue[600]!,
              Colors.blue[900]!,

              // Verdes
              Colors.green[50]!,
              Colors.green[100]!,
              Colors.green[300]!,
              Colors.green[600]!,
              Colors.green[900]!,

              // Amarillos
              Colors.yellow[50]!,
              Colors.yellow[100]!,
              Colors.yellow[300]!,
              Colors.yellow[600]!,
              Colors.yellow[900]!,

              // Naranjas
              Colors.orange[50]!,
              Colors.orange[100]!,
              Colors.orange[300]!,
              Colors.orange[600]!,
              Colors.orange[900]!,

              // Púrpuras
              Colors.purple[50]!,
              Colors.purple[100]!,
              Colors.purple[300]!,
              Colors.purple[600]!,
              Colors.purple[900]!,

              // Teals
              Colors.teal[50]!,
              Colors.teal[100]!,
              Colors.teal[300]!,
              Colors.teal[600]!,
              Colors.teal[900]!,

              // Marrones
              Colors.brown[50]!,
              Colors.brown[100]!,
              Colors.brown[300]!,
              Colors.brown[600]!,
              Colors.brown[900]!,
            ]
                .map((color) => GestureDetector(
                      onTap: () {
                        setState(() {
                          _matriz[fila][columna].colorFondo = color;
                        });
                        Navigator.pop(context);
                      },
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: color,
                          border:
                              Border.all(color: Colors.grey[400]!, width: 1),
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black12,
                              blurRadius: 2,
                              offset: Offset(0, 1),
                            ),
                          ],
                        ),
                      ),
                    ))
                .toList(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancelar'),
          ),
        ],
      ),
    );
  }

  Widget _buildCelda(int fila, int columna) {
    final celda = _matriz[fila][columna];
    final isSelected =
        _celdaSeleccionadaFila == fila && _celdaSeleccionadaColumna == columna;

    return MouseRegion(
      onEnter: (_) {
        setState(() {
          _columnaHover = columna;
          _filaHover = fila;
        });
      },
      onExit: (_) {
        // Cancelar timer anterior si existe
        _hoverTimer?.cancel();
        // Usar un delay más largo para dar tiempo a alcanzar el botón
        _hoverTimer = Timer(Duration(milliseconds: 300), () {
          if (mounted) {
            setState(() {
              _columnaHover = null;
              _filaHover = null;
            });
          }
        });
      },
      child: GestureDetector(
          onTap: () {
            setState(() {
              _celdaSeleccionadaFila = fila;
              _celdaSeleccionadaColumna = columna;
              // Limpiar selección de columna
              _columnaSeleccionada = null;
            });
          },
          onSecondaryTapDown: (details) {
            setState(() {
              _celdaSeleccionadaFila = fila;
              _celdaSeleccionadaColumna = columna;
              _columnaSeleccionada = null;
            });

            // Detectar si el click fue cerca del borde izquierdo (área de fila)
            RenderBox renderBox = context.findRenderObject() as RenderBox;
            Offset localPosition =
                renderBox.globalToLocal(details.globalPosition);

            if (localPosition.dx < 30) {
              // Click en área de fila - mostrar menú de fila
              _mostrarMenuContextualFila(context, details.globalPosition, fila);
            } else {
              // Click en celda - mostrar menú de celda
              _mostrarMenuContextualCelda(
                  context, details.globalPosition, fila, columna);
            }
          },
          child: Row(
            children: [
              Container(
                width: _anchosColumnas[columna], // Ancho personalizable
                height: double
                    .infinity, // Usar toda la altura disponible de la fila
                alignment:
                    Alignment.topLeft, // Alinear contenido arriba-izquierda
                decoration: BoxDecoration(
                  color: celda.colorFondo
                      .withValues(alpha: 0.1), // Fondo del cuadro individual
                  border: Border(
                    top: BorderSide(
                        color: isSelected ? Colors.blue : Colors.grey[300]!,
                        width: isSelected ? 2.0 : 1.0),
                    left: BorderSide(
                        color: isSelected ? Colors.blue : Colors.grey[300]!,
                        width: isSelected ? 2.0 : 1.0),
                    right: BorderSide(
                        color: isSelected ? Colors.blue : Colors.grey[300]!,
                        width: isSelected ? 2.0 : 1.0),
                    bottom: BorderSide(
                        color: isSelected ? Colors.blue : Colors.grey[300]!,
                        width: isSelected ? 2.0 : 1.0),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(6.0),
                  child: TextField(
                    onTap: () {
                      if (_celdaSeleccionadaFila != fila ||
                          _celdaSeleccionadaColumna != columna) {
                        setState(() {
                          _celdaSeleccionadaFila = fila;
                          _celdaSeleccionadaColumna = columna;
                          _columnaSeleccionada = null;
                        });
                      }
                    },
                    controller: celda.controller,
                    maxLines: null,
                    minLines: 1,
                    keyboardType: TextInputType.multiline,
                    textInputAction: TextInputAction.newline,
                    decoration: InputDecoration(
                      hintText: '',
                      border: InputBorder.none,
                      isDense: true,
                    ),
                    style: TextStyle(
                      fontSize: _fontSize,
                      height: 1.3,
                    ),
                    onChanged: (value) {
                      setState(() {
                        celda.contenido = value;
                      });

                      // Cancelar timer anterior si existe
                      _autoSaveTimer?.cancel();

                      // Crear nuevo timer para guardar estado después de 2 segundos de inactividad
                      _autoSaveTimer = Timer(Duration(seconds: 2), () {
                        _guardarEstadoEnHistorial();
                      });
                    },
                  ),
                ),
              ),
              // Divisor redimensionable (para todas las columnas)
              if (true) // Siempre mostrar divisor
                GestureDetector(
                  onPanUpdate: (details) {
                    setState(() {
                      final newAnchors = List<double>.from(_anchosColumnas);
                      newAnchors[columna] =
                          (newAnchors[columna] + details.delta.dx)
                              .clamp(50.0, 500.0);
                      _anchosColumnas = newAnchors;
                    });
                  },
                  child: MouseRegion(
                    cursor: SystemMouseCursors.resizeColumn,
                    child: Container(
                      width: 8,
                      height: double.infinity,
                      color: Colors.transparent,
                      child: Center(
                        child: Container(
                          width: 2,
                          height: double.infinity,
                          color: Colors.grey[400],
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          )),
    );
  }

  // Selector flotante de columna que aparece con hover
  Widget _buildSelectorColumnaFlotante(int columna) {
    final isSelected = _columnaSeleccionada == columna;
    final isVisible = _columnaHover == columna;

    // Calcular posición basada en anchos acumulados
    double leftPosition = 0;
    for (int i = 0; i < columna; i++) {
      leftPosition += _anchosColumnas[i];
    }
    leftPosition += _anchosColumnas[columna] / 2 -
        12; // Centro de la columna menos la mitad del botón

    return Positioned(
      left: leftPosition,
      top: -12, // Superpuesto con la línea superior de la celda
      child: MouseRegion(
        onEnter: (_) {
          // Cancelar timer de ocultación si existe
          _hoverTimer?.cancel();
          setState(() {
            _columnaHover = columna;
          });
        },
        child: AnimatedOpacity(
          opacity: isVisible ? 1.0 : 0.0,
          duration: Duration(milliseconds: 200),
          child: SizedBox(
            // Área de hover expandida
            width: 40,
            height: 40,
            child: Center(
              child: Material(
                elevation: 4,
                borderRadius: BorderRadius.circular(12),
                child: GestureDetector(
                  onTapDown: (details) {
                    setState(() {
                      _columnaSeleccionada = columna;
                      _celdaSeleccionadaFila = null;
                      _celdaSeleccionadaColumna = null;
                    });
                    // Mostrar menú contextual con click izquierdo usando la posición real
                    _mostrarMenuContextualColumna(
                        context, details.globalPosition, columna);
                  },
                  child: Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: isSelected ? Colors.blue[100] : Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected ? Colors.blue : Colors.grey[300]!,
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      Icons.more_horiz,
                      size: 12,
                      color: isSelected ? Colors.blue[700] : Colors.grey[600],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Selector flotante de fila integrado - fijo en primera línea
  Widget _buildSelectorFilaIntegrado(int fila) {
    final isVisible = _filaHover == fila;

    return Positioned(
      left: -12, // Superpuesto con la línea izquierda de la celda
      top: 6, // Fijo en la primera línea de texto de esta fila
      child: MouseRegion(
        onEnter: (_) {
          _hoverTimer?.cancel();
          setState(() {
            _filaHover = fila;
          });
        },
        onExit: (_) {
          _hoverTimer?.cancel();
          _hoverTimer = Timer(Duration(milliseconds: 300), () {
            if (mounted) {
              setState(() {
                _filaHover = null;
              });
            }
          });
        },
        child: AnimatedOpacity(
          opacity: isVisible ? 1.0 : 0.0,
          duration: Duration(milliseconds: 200),
          child: SizedBox(
            width: 40,
            height: 40,
            child: Center(
              child: GestureDetector(
                onTapDown: (TapDownDetails details) {
                  _mostrarMenuContextualFila(
                    context,
                    details.globalPosition,
                    fila,
                  );
                },
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Colors.blue[600],
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black26,
                        blurRadius: 4,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.more_horiz,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
