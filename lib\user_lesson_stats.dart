import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class UserLessonStatsPage extends StatefulWidget {
  final String userId;

  const UserLessonStatsPage({super.key, required this.userId});

  @override
  State<UserLessonStatsPage> createState() => _UserLessonStatsPageState();
}

class _UserLessonStatsPageState extends State<UserLessonStatsPage> {
  bool _loading = true;
  List<Map<String, dynamic>> _lessonStats = [];
  int? _selectedMonth;
  int? _selectedYear;

  @override
  void initState() {
    super.initState();
    if (_selectedMonth != null && _selectedYear != null) {
      _loadLessonStats(month: _selectedMonth, year: _selectedYear);
    } else {
      _loadLessonStats();
    }
  }

  Future<void> _loadLessonStats({int? month, int? year}) async {
    try {
      // Get lessons created by user
      final leccionCreadorResponse = await Supabase.instance.client
          .from('leccion_creador')
          .select('leccion_id')
          .eq('creador_id', widget.userId);

      if (leccionCreadorResponse.isEmpty) {
        setState(() {
          _lessonStats = [];
          _loading = false;
        });
        return;
      }

      final leccionIds =
          leccionCreadorResponse.map((e) => e['leccion_id']).toList();

      // Get lesson details
      final leccionesResponse = await Supabase.instance.client
          .from('lecciones')
          .select('leccion_id, leccion_nombre')
          .inFilter('leccion_id', leccionIds);

      // Prepare query for views with optional month and year filter
      var query = Supabase.instance.client
          .from('lecturas_usuario')
          .select('leccion_id, user_id, fecha_lectura')
          .inFilter('leccion_id', leccionIds);

      if (year != null) {
        DateTime startDate;
        DateTime endDate;
        if (month != null) {
          startDate = DateTime(year, month, 1);
          endDate = (month < 12)
              ? DateTime(year, month + 1, 1)
                  .subtract(const Duration(seconds: 1))
              : DateTime(year + 1, 1, 1).subtract(const Duration(seconds: 1));
        } else {
          startDate = DateTime(year, 1, 1);
          endDate =
              DateTime(year + 1, 1, 1).subtract(const Duration(seconds: 1));
        }
        query = query
            .gte('fecha_lectura', startDate.toIso8601String())
            .lte('fecha_lectura', endDate.toIso8601String());
      }

      final todasLasVisualizaciones = await query;

      // Calculate views and unique views per lesson
      final Map<int, int> vistasPorLeccion = {};
      final Map<int, Set<String>> usuariosPorLeccion = {};

      for (final vista in todasLasVisualizaciones) {
        final leccionId = vista['leccion_id'] as int;
        final userId = vista['user_id'] as String;

        vistasPorLeccion[leccionId] = (vistasPorLeccion[leccionId] ?? 0) + 1;
        usuariosPorLeccion[leccionId] ??= <String>{};
        usuariosPorLeccion[leccionId]!.add(userId);
      }

      final List<Map<String, dynamic>> stats = [];

      for (final leccion in leccionesResponse) {
        final leccionId = leccion['leccion_id'] as int;
        final leccionNombre = leccion['leccion_nombre'] as String;

        final vistas = vistasPorLeccion[leccionId] ?? 0;
        final usuariosUnicos = usuariosPorLeccion[leccionId]?.length ?? 0;

        stats.add({
          'leccion_id': leccionId,
          'leccion_nombre': leccionNombre,
          'total_vistas': vistas,
          'vistas_unicas': usuariosUnicos,
        });
      }

      setState(() {
        _lessonStats = stats;
        _loading = false;
      });
    } catch (e) {
      setState(() {
        _lessonStats = [];
        _loading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Estadísticas de Lecciones'),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                const Text('Filtrar por mes: '),
                DropdownButton<int>(
                  value: _selectedMonth,
                  hint: const Text('Mes'),
                  items: List.generate(12, (index) {
                    final month = index + 1;
                    return DropdownMenuItem(
                      value: month,
                      child: Text(month.toString().padLeft(2, '0')),
                    );
                  }),
                  onChanged: (int? newValue) {
                    setState(() {
                      _selectedMonth = newValue;
                      _loading = true;
                    });
                    if (_selectedYear != null) {
                      _loadLessonStats(
                          month: _selectedMonth, year: _selectedYear);
                    }
                  },
                ),
                const SizedBox(width: 10),
                DropdownButton<int>(
                  value: _selectedYear,
                  hint: const Text('Año'),
                  items: List.generate(10, (index) {
                    final year = DateTime.now().year - index;
                    return DropdownMenuItem(
                      value: year,
                      child: Text(year.toString()),
                    );
                  }),
                  onChanged: (int? newValue) {
                    setState(() {
                      _selectedYear = newValue;
                      _loading = true;
                    });
                    if (_selectedYear != null) {
                      _loadLessonStats(
                          month: _selectedMonth, year: _selectedYear);
                    }
                  },
                ),
                if (_selectedMonth != null || _selectedYear != null)
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _selectedMonth = null;
                        _selectedYear = null;
                        _loading = true;
                      });
                      _loadLessonStats();
                    },
                    child: const Text('Limpiar filtro'),
                  ),
              ],
            ),
          ),
          Expanded(
            child: _loading
                ? const Center(child: CircularProgressIndicator())
                : _lessonStats.isEmpty
                    ? const Center(child: Text('No hay lecciones creadas.'))
                    : SingleChildScrollView(
                        scrollDirection: Axis.vertical,
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Align(
                            alignment: Alignment.topLeft,
                            child: DataTable(
                              columns: const [
                                DataColumn(label: Text('Lección')),
                                DataColumn(label: Text('Total de Vistas')),
                                DataColumn(label: Text('Vistas Únicas')),
                              ],
                              rows: [
                                DataRow(
                                  color:
                                      WidgetStateProperty.all(Colors.grey[300]),
                                  cells: [
                                    const DataCell(Text('Total',
                                        style: TextStyle(
                                            fontWeight: FontWeight.bold))),
                                    DataCell(Text(
                                        _lessonStats
                                            .fold<int>(
                                                0,
                                                (sum, stat) =>
                                                    sum +
                                                    (stat['total_vistas']
                                                        as int))
                                            .toString(),
                                        style: const TextStyle(
                                            fontWeight: FontWeight.bold))),
                                    DataCell(Text(
                                        _lessonStats
                                            .fold<int>(
                                                0,
                                                (sum, stat) =>
                                                    sum +
                                                    (stat['vistas_unicas']
                                                        as int))
                                            .toString(),
                                        style: const TextStyle(
                                            fontWeight: FontWeight.bold))),
                                  ],
                                ),
                                ..._lessonStats.map(
                                  (stat) {
                                    final hasViews =
                                        (stat['total_vistas'] as int) > 0 ||
                                            (stat['vistas_unicas'] as int) > 0;
                                    return DataRow(
                                      color: hasViews
                                          ? WidgetStateProperty.all(
                                              Colors.grey.shade100)
                                          : null,
                                      cells: [
                                        DataCell(Text(stat['leccion_nombre'])),
                                        DataCell(Text(
                                            stat['total_vistas'].toString())),
                                        DataCell(Text(
                                            stat['vistas_unicas'].toString())),
                                      ],
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
          ),
        ],
      ),
    );
  }
}
