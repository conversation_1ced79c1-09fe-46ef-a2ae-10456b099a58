// ignore_for_file: avoid_print

import 'dart:io';
import 'dart:convert';

/// Este script lee el diccionario principal, convierte todas las palabras a minúsculas,
/// elimina duplicados que puedan surgir de la conversión y guarda el archivo actualizado.
void main() async {
  print('🚀 Iniciando script para normalizar el diccionario a minúsculas...');

  // 1. Detectar la ruta de los assets
  final currentDir = Directory.current.path;
  final isInScriptsDir = currentDir.endsWith('scripts');
  final assetsPath = isInScriptsDir ? '../assets' : 'assets';
  final filePath = '$assetsPath/diccionario_completo_final.json';
  final archivoDiccionario = File(filePath);

  print('📂 Trabajando con el archivo: ${archivoDiccionario.path}');

  // 2. Verificar si el archivo existe
  if (!await archivoDiccionario.exists()) {
    print(
        '❌ Error: El archivo de diccionario no fue encontrado en la ruta esperada.');
    return;
  }

  // 3. Leer y decodificar el JSON
  Map<String, dynamic> data;
  try {
    final contenido = await archivoDiccionario.readAsString();
    data = json.decode(contenido) as Map<String, dynamic>;
  } catch (e) {
    print(
        '❌ Error: No se pudo leer o decodificar el archivo JSON. Detalles: $e');
    return;
  }

  // 4. Procesar las palabras
  final palabrasOriginales = data['palabras'] as List<dynamic>?;

  if (palabrasOriginales == null) {
    print('❌ Error: La clave "palabras" no fue encontrada en el JSON.');
    return;
  }

  final totalOriginal = palabrasOriginales.length;
  print(
      '🔎 Se encontraron $totalOriginal palabras en el diccionario original.');

  // Convertir a minúsculas y usar un Set para eliminar duplicados
  final palabrasUnicasEnMinuscula = palabrasOriginales
      .map((p) => p.toString().trim().toLowerCase())
      .where((p) => p.isNotEmpty) // Eliminar posibles palabras vacías
      .toSet()
      .toList();

  // Ordenar alfabéticamente
  palabrasUnicasEnMinuscula.sort();

  final totalFinal = palabrasUnicasEnMinuscula.length;
  final duplicadosEliminados = totalOriginal - totalFinal;

  print('✨ Conversión a minúsculas completada.');
  if (duplicadosEliminados > 0) {
    print(
        'ℹ️ Se eliminaron $duplicadosEliminados duplicados durante el proceso (ej. "Palabra" y "palabra").');
  }
  print('📊 Total de palabras final: $totalFinal');

  // 5. Actualizar el objeto del diccionario
  data['palabras'] = palabrasUnicasEnMinuscula;
  data['total_palabras'] = totalFinal;
  data['fecha_actualizacion'] = DateTime.now().toIso8601String();

  // 6. Guardar el archivo actualizado con formato legible
  final encoder = JsonEncoder.withIndent('  ');
  final nuevoContenido = encoder.convert(data);

  await archivoDiccionario.writeAsString(nuevoContenido);
  print(
      '✅ ¡Éxito! El diccionario ha sido normalizado y guardado en: ${archivoDiccionario.path}');
}
