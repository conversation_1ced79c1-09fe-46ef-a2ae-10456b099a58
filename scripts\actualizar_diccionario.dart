import 'dart:io';
import 'dart:convert';
import 'package:logging/logging.dart';

// ignore_for_file: avoid_print
final Logger logger = Logger('ActualizarDiccionario');

void main() async {
  // Configure logger to print to console
  Logger.root.level = Level.ALL;
  Logger.root.onRecord.listen((record) => stdout
      .writeln('${record.level.name}: ${record.time}: ${record.message}'));
  // Detectar si estamos en el directorio scripts o en el directorio raíz
  final currentDir = Directory.current.path;
  final isInScriptsDir = currentDir.endsWith('scripts');
  final assetsPath = isInScriptsDir ? '../assets' : 'assets';

  // PASO 1: Cargar diccionario base actual

  final archivoBase = File('$assetsPath/diccionario_completo_final.json');

  if (!await archivoBase.exists()) {
    return;
  }

  final contenidoBase = await archivoBase.readAsString();
  final dataBase = json.decode(contenidoBase) as Map<String, dynamic>;
  final palabrasBase =
      (dataBase['palabras'] as List<dynamic>).map((p) => p.toString()).toSet();

  // PASO 2: Cargar palabras personalizadas de SharedPreferences

  final archivoPrefs = File(
      'C:/Users/<USER>/AppData/Roaming/com.example/appmanager/shared_preferences.json');
  Set<String> palabrasPersonalizadas = {};

  if (await archivoPrefs.exists()) {
    try {
      final contenidoPrefs = await archivoPrefs.readAsString();
      final dataPrefs = json.decode(contenidoPrefs) as Map<String, dynamic>;

      // Buscar la clave del diccionario personalizado
      final clavesDiccionario = dataPrefs.keys
          .where((key) => key.contains('diccionario_personalizado'))
          .toList();

      if (clavesDiccionario.isNotEmpty) {
        final clave = clavesDiccionario.first;
        final listaPalabras = dataPrefs[clave] as List<dynamic>?;

        if (listaPalabras != null) {
          palabrasPersonalizadas = listaPalabras
              .map((p) => p.toString().trim()) // Preservar formato original
              .where((p) => p.isNotEmpty && p.length >= 2)
              .toSet();

          // Mostrar algunas palabras personalizadas
          if (palabrasPersonalizadas.isNotEmpty) {
            final ejemplos = palabrasPersonalizadas.take(10).join(', ');
            print('   Ejemplos: $ejemplos');
          }
        }
      } else {}
    } catch (e) {
      logger.severe('❌ Error leyendo SharedPreferences: $e');
    }
  } else {
    logger.warning(
        '⚠️ Archivo SharedPreferences no encontrado en ${archivoPrefs.path}');
  }

  // Verificar si hay palabras para agregar
  if (palabrasPersonalizadas.isEmpty) {
    print('\nℹ️ No hay palabras nuevas para agregar al diccionario base.');
    print('   El diccionario actual tiene ${palabrasBase.length} palabras.');
    return;
  }

  // PASO 3: Combinar diccionario base con palabras personalizadas
  print('\n🔧 PASO 3: Combinando diccionarios...');

  // Verificar cuáles palabras son realmente nuevas
  final palabrasRealmenteNuevas = palabrasPersonalizadas
      .where((palabra) => !palabrasBase.contains(palabra))
      .toSet();

  final duplicadosEncontrados =
      palabrasPersonalizadas.length - palabrasRealmenteNuevas.length;

  // Combinar usando Set (elimina duplicados automáticamente)
  final palabrasCombinadas = <String>{};
  palabrasCombinadas.addAll(palabrasBase);
  palabrasCombinadas.addAll(palabrasPersonalizadas);

  print('📊 Estadísticas combinación:');
  print('   - Diccionario base: ${palabrasBase.length}');
  print('   - Palabras personalizadas: ${palabrasPersonalizadas.length}');
  print('   - Palabras realmente nuevas: ${palabrasRealmenteNuevas.length}');
  print('   - Duplicados (ya existían): $duplicadosEncontrados');
  print('   - Total final: ${palabrasCombinadas.length}');

  // Debug: Mostrar palabras nuevas vs duplicados
  if (palabrasRealmenteNuevas.isNotEmpty) {
    print('\n🆕 Palabras nuevas que se agregarán:');
    // ignore: unused_local_variable
    for (final palabra in palabrasRealmenteNuevas.take(5)) {
      print('   - "$palabra"');
    }
    if (palabrasRealmenteNuevas.length > 5) {
      print('   ... y ${palabrasRealmenteNuevas.length - 5} más');
    }
  }

  if (duplicadosEncontrados > 0) {
    final duplicados = palabrasPersonalizadas
        .where((palabra) => palabrasBase.contains(palabra))
        .take(5);
    print('\n🔄 Palabras que ya existían:');
    // ignore: unused_local_variable
    for (final palabra in duplicados) {
      print('   - "$palabra"');
    }
  }

  // PASO 4: Ordenamiento alfabético
  print('\n🔤 PASO 4: Ordenando alfabéticamente...');
  final palabrasFinales = palabrasCombinadas.toList()..sort();

  // PASO 5: Crear JSON actualizado
  print('\n💾 PASO 5: Generando diccionario actualizado...');
  final diccionarioActualizado = {
    'version': 'diccionario_completo_final_v1',
    'fuentes': [
      'Diccionario Base Anterior',
      'Palabras Personalizadas del Usuario'
    ],
    'fecha_actualizacion': DateTime.now().toIso8601String(),
    'palabras': palabrasFinales,
    'total_palabras': palabrasFinales.length,
    'estadisticas': {
      'diccionario_base_anterior': palabrasBase.length,
      'palabras_realmente_nuevas': palabrasRealmenteNuevas.length,
      'duplicados_encontrados': duplicadosEncontrados,
      'total_final': palabrasFinales.length,
    },
    'metadatos': {
      'ordenamiento': 'alfabetico',
      'actualizacion': 'incremental',
      'preserva_formato': 'si',
    }
  };

  // PASO 6: Guardar archivo actualizado
  await archivoBase.writeAsString(json.encode(diccionarioActualizado));

  print('✅ Diccionario base actualizado en: ${archivoBase.path}');

  // PASO 7: Verificación final
  print('\n🧪 PASO 7: Verificación de palabras agregadas:');

  if (palabrasPersonalizadas.isNotEmpty) {
    print('   Palabras que se agregaron al diccionario base:');
    for (final palabra in palabrasPersonalizadas.take(10)) {
      final bool integrada = palabrasFinales.contains(palabra);
      print('   - "$palabra": ${integrada ? "✅ Integrada" : "❌ No integrada"}');
    }

    if (palabrasPersonalizadas.length > 10) {
      print('   ... y ${palabrasPersonalizadas.length - 10} palabras más');
    }
  }

  // PASO 8: Resumen final
  print('\n📈 RESUMEN FINAL:');
  print(
      '   📊 Diccionario base actualizado: ${palabrasFinales.length} palabras');
  print('   📈 Incremento: +${palabrasRealmenteNuevas.length} palabras nuevas');
  print('   🔄 Duplicados encontrados: $duplicadosEncontrados');
  print('   🎯 Ordenamiento: Alfabético completo');
  print('   💾 Archivo: diccionario_completo_final.json');
  print('   ✅ Actualización completada exitosamente');

  print('\n💡 SIGUIENTE PASO:');
  print('   Reabre la aplicación para cargar el diccionario actualizado');
}
