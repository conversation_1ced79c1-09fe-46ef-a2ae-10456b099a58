import 'dart:async';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:logging/logging.dart';
import 'package:dio/dio.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final _logger = Logger('DatabaseHelper');

class DatabaseHelper {
  final _controller = StreamController<List<Map<String, dynamic>>>.broadcast();
  Stream<List<Map<String, dynamic>>> get recentLeccionesStream =>
      _controller.stream;

  Future<Map<String, dynamic>> fetchAllDataFromSupabase() async {
    try {
      final supabaseUrl = dotenv.env['SUPABASE_URL'];
      final supabaseKey = dotenv.env['SUPABASE_ANON_KEY'];

      if (supabaseUrl == null || supabaseKey == null) {
        throw Exception(
            'Supabase URL or Key is not set in environment variables');
      }

      // Obtener el token del usuario autenticado
      final user = Supabase.instance.client.auth.currentUser;
      final session = Supabase.instance.client.auth.currentSession;

      if (user == null || session == null) {
        throw Exception('User is not authenticated');
      }

      final accessToken = session.accessToken;

      final dio = Dio();
      final response = await dio.post(
        '$supabaseUrl/rest/v1/rpc/get_all_data_total',
        options: Options(
          headers: {
            'apikey': supabaseKey,
            'Authorization':
                'Bearer $accessToken', // Usar el token de acceso del usuario
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        return response.data;
      } else {
        _logger.severe('Failed to load data: ${response.statusCode}');
        throw Exception('Failed to load data: ${response.statusCode}');
      }
    } catch (e) {
      _logger.severe('Failed to fetch data from Supabase: $e');
      throw Exception('Failed to fetch data from Supabase: $e');
    }
  }
}
