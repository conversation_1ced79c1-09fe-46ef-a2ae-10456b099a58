import 'package:flutter/material.dart';
import 'database_helper.dart';
import 'package:logging/logging.dart';
import 'dart:async';
import 'dart:io';
import 'package:path/path.dart' as path;
import 'dart:convert';

class DownloadPage extends StatefulWidget {
  const DownloadPage({super.key});

  @override
  DownloadPageState createState() => DownloadPageState();
}

class DownloadPageState extends State<DownloadPage> {
  bool _isDownloading = false;
  final dbHelper = DatabaseHelper();
  final Logger _logger = Logger('DownloadPage');
  final StreamController<double> _progressController =
      StreamController<double>();

  @override
  void initState() {
    super.initState();
    _downloadJsonFile();
  }

  @override
  void dispose() {
    _progressController.close();
    super.dispose();
  }

  Future<void> _downloadJsonFile() async {
    try {
      final jsonData = await dbHelper.fetchAllDataFromSupabase();
      await _saveJsonToFile(jsonData);
    } catch (e) {
      _logger.severe('Error downloading JSON file: $e');
    }
  }

  Future<void> _saveJsonToFile(Map<String, dynamic> jsonData) async {
    try {
      final directory =
          Directory('C:\\FlutterProjects\\appmanager\\Galeria medicapp');
      if (!directory.existsSync()) {
        directory.createSync(recursive: true);
      }

      final filePath = path.join(directory.path, 'data.json');
      final file = File(filePath);

      await file.writeAsString(jsonEncode(jsonData), encoding: utf8);

      final fileSizeBytes = await file.length();
      final fileSizeKB = fileSizeBytes / 1024;
      _logger.info(
          'JSON file saved at $filePath with size: ${fileSizeKB.toStringAsFixed(2)} KB');
    } catch (e) {
      _logger.severe('Error saving JSON file: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Download Data'),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Download offline content',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                              height: 20,
                              child: _isDownloading
                                  ? const Text('Downloading...')
                                  : null,
                            ),
                            const SizedBox(height: 4),
                            StreamBuilder<double>(
                              stream: _progressController.stream,
                              builder: (context, snapshot) {
                                return LinearProgressIndicator(
                                  value: snapshot.data ?? 0.0,
                                  backgroundColor: Colors.grey[700],
                                  valueColor:
                                      const AlwaysStoppedAnimation<Color>(
                                          Colors.green),
                                );
                              },
                            ),
                            const SizedBox(height: 4),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(''),
                                Text(''),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 8),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          _isDownloading
                              ? IconButton(
                                  icon: const Icon(Icons.stop),
                                  onPressed: () {
                                    setState(() {
                                      _isDownloading = false;
                                    });
                                  },
                                )
                              : IconButton(
                                  icon: const Icon(Icons.play_arrow),
                                  onPressed: () {
                                    setState(() {
                                      _isDownloading = true;
                                    });
                                    _downloadJsonFile();
                                  },
                                  iconSize: 24.0,
                                ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                      'Last update: ${DateTime.now().toString().substring(0, 16)}'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
