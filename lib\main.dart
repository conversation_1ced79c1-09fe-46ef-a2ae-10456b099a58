import 'dart:io';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart' as dotenv;
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'homepage.dart';
import 'mi_cuenta.dart';
import 'leccion_edit.dart';
import 'leccionmanager.dart';
import 'datamanagement.dart';
import 'nuevadefinicion.dart';
import 'listadefiniciones.dart';
import 'listausuarios.dart';
import 'usuario_edit.dart';
import 'notificationmanager.dart';
import 'package:supabase_auth_ui/supabase_auth_ui.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'new_user.dart';
import 'animated_wave_background.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if (!kIsWeb) {
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    }
  }

  await dotenv.dotenv.load(fileName: "supabasekeys.env");

  await Supabase.initialize(
    url: dotenv.dotenv.env['SUPABASE_URL']!,
    anonKey: dotenv.dotenv.env['SUPABASE_ANON_KEY']!,
  );

  bool permissionGranted = await requestStoragePermission();
  if (!permissionGranted) {
    runApp(const PermissionDeniedApp());
    return;
  }

  runApp(const MyApp());
}

Future<bool> requestStoragePermission() async {
  var statusImages = await Permission.photos.status;
  var statusVideos = await Permission.videos.status;
  var statusAudio = await Permission.audio.status;

  if (statusImages.isDenied || statusImages.isPermanentlyDenied) {
    statusImages = await Permission.photos.request();
  }
  if (statusVideos.isDenied || statusVideos.isPermanentlyDenied) {
    statusVideos = await Permission.videos.request();
  }
  if (statusAudio.isDenied || statusAudio.isPermanentlyDenied) {
    statusAudio = await Permission.audio.request();
  }

  if (statusImages.isGranted &&
      statusVideos.isGranted &&
      statusAudio.isGranted) {
    return true;
  } else {
    if (statusImages.isDenied ||
        statusVideos.isDenied ||
        statusAudio.isDenied) {
      // Permission is denied by the user.
    } else if (statusImages.isPermanentlyDenied ||
        statusVideos.isPermanentlyDenied ||
        statusAudio.isPermanentlyDenied) {
      // Permission is permanently denied. The user must enable it from the app settings.
      openAppSettings();
    } else if (statusImages.isRestricted ||
        statusVideos.isRestricted ||
        statusAudio.isRestricted) {
      // Permission is restricted and cannot be requested.
    } else if (statusImages.isLimited ||
        statusVideos.isLimited ||
        statusAudio.isLimited) {
      // Permission is limited.
    }
    return false;
  }
}

class PermissionDeniedApp extends StatelessWidget {
  const PermissionDeniedApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Permission Denied'),
        ),
        body: Center(
          child: Text(
            'Storage permission not granted. Please enable it in the app settings.',
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Your App',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const AuthWrapper(),
      routes: {
        '/HomePage': (context) => const HomePage(),
        '/miCuenta': (context) => const MiCuentaPage(),
        '/nuevaLeccion': (context) => const LeccionEditPage(leccionId: null),
        '/editarLecciones': (context) => const LeccionManagerPage(),
        '/editar': (context) => const LeccionManagerPage(),
        '/Datamanagement': (context) => const Datamanagement(),
        '/nuevaDefinicion': (context) => const NuevaDefinicion(),
        '/listaDefiniciones': (context) => const ListaDefiniciones(),
        '/usuarios': (context) => const ListaUsuariosPage(),
        '/usuario_edit': (context) => const UsuarioEditPage(),
        '/notificationManager': (context) => const NotificationManager(),
      },
    );
  }
}

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<AuthState>(
      stream: Supabase.instance.client.auth.onAuthStateChange,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.active) {
          if (snapshot.hasData && snapshot.data!.session != null) {
            return const HomePage();
          } else {
            return const LoginPage();
          }
        } else {
          return Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }
      },
    );
  }
}

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  bool _obscurePassword = true;

  Future<void> _handleSignIn(String email, String password) async {
    await Supabase.instance.client.auth.signInWithPassword(
      email: email.trim(),
      password: password,
    );
  }

  @override
  void dispose() {
    emailController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A1A),
      appBar: AppBar(
        title: const Text('Login'),
        backgroundColor: const Color(0xFF1A1A1A),
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Center(
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Title with animated metallic gray wave background
                SizedBox(
                  height: 100,
                  child: Stack(
                    children: [
                      Positioned(
                        top: -40,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        child: AnimatedWaveText(),
                      ),
                      Center(),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFF2A2A2A),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black,
                        spreadRadius: 2,
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      ConstrainedBox(
                        constraints: const BoxConstraints(
                          minWidth: 300,
                          maxWidth: 400,
                        ),
                        child: TextField(
                          controller: emailController,
                          style: const TextStyle(color: Colors.white),
                          decoration: const InputDecoration(
                            labelText: 'Email',
                            labelStyle: TextStyle(color: Colors.white70),
                            enabledBorder: UnderlineInputBorder(
                              borderSide: BorderSide(color: Colors.white38),
                            ),
                            focusedBorder: UnderlineInputBorder(
                              borderSide: BorderSide(color: Color(0xFF00FF00)),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      ConstrainedBox(
                        constraints: const BoxConstraints(
                          minWidth: 300,
                          maxWidth: 400,
                        ),
                        child: TextField(
                          controller: passwordController,
                          style: const TextStyle(color: Colors.white),
                          decoration: InputDecoration(
                            labelText: 'Password',
                            labelStyle: const TextStyle(color: Colors.white70),
                            enabledBorder: const UnderlineInputBorder(
                              borderSide: BorderSide(color: Colors.white38),
                            ),
                            focusedBorder: const UnderlineInputBorder(
                              borderSide: BorderSide(color: Color(0xFF00FF00)),
                            ),
                            suffixIcon: GestureDetector(
                              onTapDown: (_) {
                                setState(() {
                                  _obscurePassword = false;
                                });
                              },
                              onTapUp: (_) {
                                setState(() {
                                  _obscurePassword = true;
                                });
                              },
                              onTapCancel: () {
                                setState(() {
                                  _obscurePassword = true;
                                });
                              },
                              child: Icon(
                                _obscurePassword
                                    ? Icons.visibility_off
                                    : Icons.visibility,
                                color: Colors.white70,
                              ),
                            ),
                          ),
                          obscureText: _obscurePassword,
                        ),
                      ),
                      const SizedBox(height: 20),
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF00FF00),
                          foregroundColor: Colors.black,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 32, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        onPressed: () => _handleSignIn(
                          emailController.text,
                          passwordController.text,
                        ),
                        child: const Text('Login'),
                      ),
                      TextButton(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => const NewUserPage()),
                          );
                        },
                        child: const Text(
                          'Need an account? Sign Up',
                          style: TextStyle(color: Colors.white70),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
