import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'homepage_componentes/drawer_homepage.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final int _selectedIndex = 0;
  String? _userRole;
  bool _isLoadingRole = true;

  @override
  void initState() {
    super.initState();

    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // Actualizar última actividad
    _updateLastActivity();

    // Load user role
    _loadUserRole();
  }

  Future<void> _loadUserRole() async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) return;

      final response = await Supabase.instance.client
          .from('profiles')
          .select('roles(role_name)')
          .eq('user_id', user.id)
          .single();

      print('DEBUG: Role response: $response');

      setState(() {
        _userRole = response['roles']?['role_name'] as String?;
        _isLoadingRole = false;
      });
    } catch (e) {
      print('DEBUG: Error loading user role: \$e');
      setState(() {
        _isLoadingRole = false;
      });
    }
  }

  Future<void> _updateLastActivity() async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user != null) {
        print('DEBUG: Updating last_activity_at for user ${user.id}');
        await Supabase.instance.client.from('profiles').update({
          'last_activity_at': DateTime.now().toUtc().toIso8601String()
        }).eq('user_id', user.id);
        print('DEBUG: last_activity_at updated successfully');
      }
    } catch (e) {
      print('DEBUG: Error updating last_activity_at: $e');
    }
  }

  @override
  void dispose() {
    SystemChrome.setPreferredOrientations(DeviceOrientation.values);
    super.dispose();
  }

  void _logout() async {
    await Supabase.instance.client.auth.signOut();
  }

  @override
  Widget build(BuildContext context) {
    const routes = [
      '/miCuenta',
      '/editarLecciones',
      '/notificationManager',
      '/nuevaDefinicion',
      '/listaDefiniciones',
      '/usuarios',
      '/Datamanagement',
    ];
    const titles = [
      'Mi cuenta',
      'Editar Lecciones',
      'Gestionar Notificaciones',
      'Nueva Definición',
      'Lista Definiciones',
      'Usuarios',
      'Data management',
    ];
    const icons = [
      Icons.account_circle,
      Icons.edit,
      Icons.notifications,
      Icons.photo_library,
      Icons.description,
      Icons.list_alt,
      Icons.people,
      Icons.storage,
    ];

    const categories = {
      'Perfil': [0],
      'Contenido': [1, 3, 4],
      'Administración': [2, 5, 6],
    };

    if (_isLoadingRole) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final pages = [
      Scaffold(
        backgroundColor: const Color(0xFF1A1A1A),
        appBar: AppBar(
          title: const Text('Panel de Control',
              style:
                  TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
          backgroundColor: const Color(0xFF1A1A1A),
          elevation: 0,
          actions: <Widget>[
            IconButton(
              icon: const Icon(Icons.logout, color: Colors.white),
              onPressed: _logout,
            ),
          ],
        ),
        drawer: DrawerHomePage(client: Supabase.instance.client),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFF2A2A2A),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black,
                      spreadRadius: 2,
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: const Row(
                  children: [
                    Icon(Icons.dashboard, color: Color(0xFF00FF00), size: 32),
                    SizedBox(width: 16),
                    Text(
                      'Bienvenido al Panel de Control',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              Expanded(
                child: ListView.builder(
                  itemCount: categories.length,
                  itemBuilder: (context, categoryIndex) {
                    final categoryName =
                        categories.keys.elementAt(categoryIndex);
                    final categoryItems =
                        categories.values.elementAt(categoryIndex);

                    // Filter icons based on user role
                    List<int> filteredCategoryItems =
                        categoryItems.where((itemIndex) {
                      if (_userRole == 'superadmin') {
                        return true; // superadmin sees all
                      } else if (_userRole == 'editor') {
                        // "Editar Lecciones" (index 1) and "Mi cuenta" (index 0) visible to editor only
                        return itemIndex == 0 || itemIndex == 1;
                      } else {
                        // Other roles see only "Mi cuenta"
                        return itemIndex == 0;
                      }
                    }).toList();

                    if (filteredCategoryItems.isEmpty) {
                      return const SizedBox.shrink();
                    }

                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: Text(
                            categoryName,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF00FF00),
                            ),
                          ),
                        ),
                        GridView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            childAspectRatio: 1.2,
                            mainAxisSpacing: 10,
                            crossAxisSpacing: 10,
                          ),
                          itemCount: filteredCategoryItems.length,
                          itemBuilder: (context, index) {
                            final itemIndex = filteredCategoryItems[index];
                            return Card(
                              color: const Color(0xFF2A2A2A),
                              elevation: 5,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: InkWell(
                                borderRadius: BorderRadius.circular(12),
                                onTap: () {
                                  Navigator.pushNamed(
                                      context, routes[itemIndex]);
                                },
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      ShaderMask(
                                        shaderCallback: (bounds) =>
                                            const LinearGradient(
                                          colors: [
                                            Color(0xFF00FF00),
                                            Color(0xFF00BFFF)
                                          ],
                                          tileMode: TileMode.mirror,
                                        ).createShader(bounds),
                                        child: Icon(icons[itemIndex],
                                            size: 32, color: Colors.white),
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        titles[itemIndex],
                                        textAlign: TextAlign.center,
                                        style: const TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                        const SizedBox(height: 16),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    ];

    return Scaffold(
      body: IndexedStack(
        index: _selectedIndex,
        children: pages,
      ),
    );
  }
}
