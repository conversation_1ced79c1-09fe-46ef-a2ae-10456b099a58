// ignore_for_file: avoid_print

import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart'; // Importar TapGestureRecognizer
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';
import 'package:appmanager/leccionpage_componentes/drawer_leccionpage.dart';
import 'package:appmanager/leccionpage_componentes/bottomnavigation.dart';
// Importar bottomsheet.dart con alias
import 'imagenview.dart';

class Item {
  String headerValue;
  String tag;
  List<Item> children;
  BuildContext? context;
  bool isExpanded;
  bool hasBulletIcon;
  GlobalKey key;
  String additionalContent;
  int level;

  Item({
    required this.headerValue,
    required this.tag,
    this.context,
    this.isExpanded = false,
    this.children = const [],
    this.hasBulletIcon = false,
    required this.key,
    this.additionalContent = '',
    this.level = 0,
  });
}

String safeEncodeUri(String uri) {
  return Uri.encodeComponent(uri);
}

String safeDecodeUri(String uri) {
  try {
    // Verificar si la URI ya está decodificada
    final decodedUri = Uri.decodeComponent(uri);
    if (Uri.encodeComponent(decodedUri) == uri) {
      return decodedUri;
    }
    return uri;
  } catch (e) {
    // Manejar el error de manera silenciosa
    return uri; // Devuelve la URI original si hay un error
  }
}

bool isValidUri(String uri) {
  try {
    Uri.parse(uri);
    return true;
  } catch (e) {
    return false;
  }
}

String createLink(String text, String link) {
  return '[$text](${safeEncodeUri(link)})';
}

class LeccionPage extends StatefulWidget {
  final String titulo;
  final String? contenido; // Ahora es opcional
  final int leccionId;
  const LeccionPage({
    super.key,
    required this.titulo,
    this.contenido, // Opcional - se cargará desde Supabase si no se proporciona
    required this.leccionId,
    required leccionId2,
  });

  @override
  // ignore: library_private_types_in_public_api
  _LeccionPageState createState() => _LeccionPageState();
}

class _LeccionPageState extends State<LeccionPage> {
  List<Item> _data = [];
  final Map<String, GlobalKey> keys = {};
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final ScrollController _scrollController = ScrollController();
  String _searchQuery = ''; // Añadir variable para la búsqueda
  final ValueNotifier<int> _matchCountNotifier =
      ValueNotifier<int>(0); // Notificador de coincidencias
  final ValueNotifier<int> _currentMatchNotifier =
      ValueNotifier<int>(0); // Notificador de coincidencia actual
  // Variables para manejar las coincidencias de búsqueda
  final List<GlobalKey> _matchKeys = [];
  int _currentMatchIndex = 0;

  // Variables para cargar contenido desde Supabase
  String? _contenidoActual;
  bool _cargando = false;
  final Logger _logger = Logger();
  final Map<String, bool> _checkboxStates = {};

  final Map<String, TextStyle> hStyles = {
    'h1': const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.bold,
        color: Colors.black,
        fontFamily: 'TextStyle'),
    'h2': const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Colors.black,
        fontFamily: 'TextStyle'),
    'h3': const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: Colors.black,
        fontFamily: 'TextStyle'),
    'h4': const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.bold,
        color: Colors.black,
        fontFamily: 'TextStyle'),
    'a': const TextStyle(
      color: Colors.black,
      decoration: TextDecoration.underline,
      decorationColor: Colors.black,
      decorationStyle: TextDecorationStyle.dashed,
      fontFamily: 'TextStyle',
      fontSize: 14,
    ),
    'imageLink': const TextStyle(
      color: Colors.blue,
      decoration: TextDecoration.underline,
      decorationColor: Colors.blue,
      decorationStyle: TextDecorationStyle.solid,
      fontFamily: 'TextStyle',
      fontSize: 14,
    ),
    'listBullet': const TextStyle(
        color: Colors.black,
        fontSize: 14,
        fontWeight: FontWeight.normal,
        fontFamily: 'TextStyle'),
    'tableHead': const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: Colors.black,
        fontFamily: 'TextStyle'),
    'tableBody': const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.normal,
        color: Colors.black,
        fontFamily: 'TextStyle'),
    'strong': const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.bold,
        color: Colors.black,
        fontFamily: 'TextStyle'),
    'em': const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.normal,
        fontStyle: FontStyle.italic,
        color: Colors.black,
        fontFamily: 'TextStyle'),
  };
  @override
  void initState() {
    super.initState();
    _inicializarContenido();
  }

  // Inicializar contenido - SIEMPRE cargar desde Supabase para contenido fresco
  Future<void> _inicializarContenido() async {
    // Mostrar contenido del cache temporalmente mientras carga
    if (widget.contenido != null && widget.contenido!.isNotEmpty) {
      setState(() {
        _contenidoActual = widget.contenido!;
        _data = _parseMarkdown(_contenidoActual!);
      });
    }

    // SIEMPRE cargar desde Supabase para obtener contenido fresco
    await _cargarContenidoDesdeSupabase();
  }

  // Cargar contenido fresco desde Supabase
  Future<void> _cargarContenidoDesdeSupabase() async {
    setState(() {
      _cargando = true;
    });

    try {
      _logger.i(
          '🔄 Cargando contenido fresco desde Supabase para lección: ${widget.titulo}');

      final response = await Supabase.instance.client
          .from('lecciones')
          .select('leccion_contenido')
          .eq('leccion_nombre', widget.titulo)
          .single();

      final contenido = response['leccion_contenido'] as String? ?? '';

      _logger.i(
          '✅ Contenido cargado desde Supabase: ${contenido.length > 50 ? "${contenido.substring(0, 50)}..." : contenido}');

      setState(() {
        _contenidoActual = contenido;
        _data = _parseMarkdown(contenido);
        _cargando = false;
      });
    } catch (e) {
      _logger.e('❌ Error cargando contenido desde Supabase: $e');
      _logger.w('🔄 Usando contenido del cache como fallback');
      setState(() {
        _contenidoActual = widget.contenido ?? '';
        _data = _parseMarkdown(_contenidoActual ?? '');
        _cargando = false;
      });
    }
  }

  List<Item> _parseMarkdown(String markdownContent) {
    final List<Item> items = [];
    final lines = markdownContent.split('\n');
    bool inTable = false;
    String tableContent = '';
    final RegExp listItemRegExp = RegExp(r'^(\s*)-\s*(.*)');
    final RegExp headerRegExp = RegExp(r'^(#+)\s*(.*)');
    final RegExp cuadroRegExp = RegExp(r'^\[CUADRO:(\d+)\]');

    for (var line in lines) {
      print('Processing line: $line'); // Agregar print para depuración
      final headerMatch = headerRegExp.firstMatch(line);
      if (headerMatch != null) {
        if (inTable) {
          final key = GlobalKey();
          keys[tableContent.trim()] = key;
          items.add(Item(
              headerValue: tableContent.trim(),
              tag: 'table',
              key: key,
              level: 0));
          tableContent = '';
          inTable = false;
        }
        final headerLevel = headerMatch.group(1)!.length;
        final headerValue = headerMatch.group(2)!.trim();
        print('Header detected: $headerValue'); // Agregar print para depuración
        final tag = 'h$headerLevel';
        final key = GlobalKey();
        keys[line.trim()] = key; // Usar la línea completa para la clave
        items.add(Item(headerValue: headerValue, tag: tag, key: key, level: 0));
        final RegExp checkboxListItemRegExp = RegExp(r'^(\s*)\[\]\s+(.*)');

        final checkboxMatch = checkboxListItemRegExp.firstMatch(line);
        if (checkboxMatch != null) {
          final indentSpaces = checkboxMatch.group(1) ?? '';
          final text = checkboxMatch.group(2) ?? '';
          final level = _getIndentationLevel(indentSpaces);
          final key = GlobalKey();
          keys[line.trim()] = key;
          items.add(Item(
              headerValue: text, tag: 'checkboxList', key: key, level: level));
          continue; // Skip further processing for this line
        }
      } else if (listItemRegExp.hasMatch(line)) {
        if (inTable) {
          // Si estamos en una tabla, agregar la línea de lista al contenido de la tabla
          tableContent += '$line\n';
        } else {
          // Si no estamos en una tabla, procesar como elemento de lista normal
          final match = listItemRegExp.firstMatch(line);
          final headerValue =
              match?.group(2)?.trim() ?? ''; // Usar el texto completo
          print(
              'List item detected: $headerValue'); // Agregar print para depuración
          final level = _getIndentationLevel(match?.group(1) ?? '');
          final key = GlobalKey();
          keys[line.trim()] = key; // Usar la línea completa para la clave
          items.add(Item(
              headerValue: headerValue, tag: 'li', key: key, level: level));
        }
      } else if (cuadroRegExp.hasMatch(line.trim())) {
        // Detectar referencias a cuadros [CUADRO:id]
        if (inTable) {
          final key = GlobalKey();
          keys[tableContent.trim()] = key;
          items.add(Item(
              headerValue: tableContent.trim(),
              tag: 'table',
              key: key,
              level: 0));
          tableContent = '';
          inTable = false;
        }
        final match = cuadroRegExp.firstMatch(line.trim());
        final cuadroId = match?.group(1) ?? '';
        print('Cuadro detected: ID $cuadroId'); // Agregar print para depuración
        final key = GlobalKey();

        // AGREGAR LA CLAVE DEL CUADRO AL MAPA keys
        keys[line.trim()] = key;

        items.add(
            Item(headerValue: cuadroId, tag: 'cuadro', key: key, level: 0));
      } else if (line.trim().isNotEmpty) {
        if (line.trim().startsWith('|')) {
          inTable = true;
          tableContent += '$line\n';
        } else {
          if (inTable) {
            // Si estamos en una tabla y la línea no empieza con |,
            // podría ser contenido adicional de la celda anterior
            // Solo terminar la tabla si no es una línea de lista
            if (!line.trim().startsWith('-') &&
                !line.trim().startsWith('*') &&
                !line.trim().startsWith('•')) {
              final key = GlobalKey();
              keys[tableContent.trim()] = key;
              items.add(Item(
                  headerValue: tableContent.trim(),
                  tag: 'table',
                  key: key,
                  level: 0));
              tableContent = '';
              inTable = false;

              final key2 = GlobalKey();
              keys[line.trim()] = key2;
              items.add(Item(
                  headerValue: line.trim(), tag: 'p', key: key2, level: 0));
            }
            // Si es una línea de lista, ya se manejó en el bloque anterior
          } else {
            final key = GlobalKey();
            keys[line.trim()] = key; // Usar la línea completa para la clave
            items.add(
                Item(headerValue: line.trim(), tag: 'p', key: key, level: 0));
          }
        }
      }
    }
    if (inTable) {
      final key = GlobalKey();
      keys[tableContent.trim()] = key;
      items.add(Item(
          headerValue: tableContent.trim(), tag: 'table', key: key, level: 0));
    }
    return items;
  }

  int _getIndentationLevel(String line) {
    int level = 0;
    while (line.startsWith('  ')) {
      level++;
      line = line.substring(2);
    }
    return level;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color.fromARGB(255, 255, 255, 255),
      key: _scaffoldKey,
      appBar: AppBar(
        backgroundColor: Color.fromARGB(255, 169, 210, 232),
        leading: const BackButton(),
        title: Text(
          widget.titulo,
          style: const TextStyle(
            color: Color.fromARGB(255, 32, 22, 110),
            fontWeight: FontWeight.bold, // Cambia el texto a negrita
            fontSize: 16.0, // Ajusta el tamaño del texto
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            tooltip: 'Actualizar contenido',
            onPressed: _cargarContenidoDesdeSupabase,
          ),
        ],
      ),
      drawer: LeccionDrawer(
        htmlContent: _contenidoActual ?? '',
        scaffoldKey: _scaffoldKey,
        keys: keys,
      ),
      body: _cargando
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Cargando contenido desde Supabase...'),
                ],
              ),
            )
          : Padding(
              padding: const EdgeInsets.all(8.0),
              child: SelectionArea(
                // Envolver el contenido en un SelectionArea
                child: SingleChildScrollView(
                  controller: _scrollController,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: _buildItems(_data),
                  ),
                ),
              ),
            ),
      bottomNavigationBar: BottomNavigation(
        scaffoldKey: _scaffoldKey,
        onSearch: _onSearch,
        matchCountNotifier: _matchCountNotifier,
        currentMatchNotifier: _currentMatchNotifier,
        onPreviousMatch: _scrollToPreviousMatch,
        onNextMatch: _scrollToNextMatch,
      ),
    );
  }

  void _onSearch(String query) {
    setState(() {
      _searchQuery = query;
      _matchCountNotifier.value = 0; // Resetear contador de coincidencias
      _currentMatchNotifier.value = 0; // Resetear coincidencia actual
      _matchKeys.clear(); // Limpiar las claves de coincidencias anteriores
      _currentMatchIndex = 0; // Resetear el índice de coincidencia actual
      _data = _parseMarkdown(
          _contenidoActual ?? ''); // Volver a parsear el contenido

      // Si hay coincidencias, actualizar el notificador de coincidencias
      if (_matchKeys.isNotEmpty) {
        _matchCountNotifier.value = _matchKeys.length;
        _currentMatchIndex = 0; // Inicializar el índice interno en 0
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToCurrentMatch(); // Desplazarse a la primera coincidencia
          _currentMatchNotifier.value = 1; // Actualizar coincidencia actual a 1
        });
      }
    });
  }

  List<Widget> _buildItems(List<Item> data) {
    List<Widget> items = [];
    List<Item> checkboxGroup = [];

    for (int i = 0; i < data.length; i++) {
      if (data[i].tag == 'checkboxList') {
        // Agregar al grupo de checkboxes
        checkboxGroup.add(data[i]);

        // Si es el último elemento o el siguiente no es un checkbox, procesar el grupo
        if (i == data.length - 1 || data[i + 1].tag != 'checkboxList') {
          items.add(
            Padding(
              padding: EdgeInsets.only(
                top: 12.0, // Padding superior para separar del widget anterior
                bottom:
                    8.0, // Padding inferior para separar del widget siguiente
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: checkboxGroup
                    .map((item) => _buildCheckboxListItem(item))
                    .toList(),
              ),
            ),
          );
          checkboxGroup.clear(); // Limpiar el grupo
        }
      } else if (data[i].tag == 'table') {
        items.add(
          Padding(
            padding:
                const EdgeInsets.symmetric(vertical: 10.0, horizontal: 16.0),
            child: _buildTable(data[i].headerValue),
          ),
        );
      } else if (data[i].tag == 'cuadro') {
        items.add(
          Padding(
            padding:
                const EdgeInsets.symmetric(vertical: 10.0, horizontal: 16.0),
            child: _buildCuadro(data[i].headerValue),
          ),
        );
      } else {
        items.add(
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0),
            child: _buildItem(data[i]),
          ),
        );
      }

      if (data[i].tag == 'h1' || data[i].tag == 'h2') {
        items.add(const Divider(height: 1.0));
      }
    }

    return items;
  }

  Widget _buildItem(Item item) {
    if (item.tag == 'p' || item.tag.startsWith('h') || item.tag == 'li') {
      return Padding(
        padding: EdgeInsets.symmetric(
          vertical: 4.0,
          horizontal: 16.0 +
              item.level *
                  20.0, // Ajusta el padding aquí para respetar los niveles de indentación
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          key: item.key,
          children: [
            if (item.tag == 'li')
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getBulletText(item
                        .level), // Obtener el texto del bullet según el nivel
                    style: hStyles['listBullet'],
                  ),
                  const SizedBox(
                      width: 4.0), // Espacio entre el bullet y el texto
                  Expanded(
                    child: Text.rich(
                      TextSpan(children: _buildTextSpans(item)),
                    ),
                  ),
                ],
              )
            else
              Text.rich(
                TextSpan(children: _buildTextSpans(item)),
              ),
          ],
        ),
      );
    } else {
      return Padding(
        padding: EdgeInsets.zero,
        child: DefaultTextStyle(
          style: const TextStyle(decorationColor: Colors.white),
          child: Text(item.headerValue, key: item.key),
        ),
      );
    }
  }

  Widget _buildCheckboxListItem(Item item) {
    bool checked = _checkboxStates[item.headerValue] ?? false;

    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: 2.0, // Aumentado de 1.0 a 2.0 para más espacio entre líneas
        horizontal: 16.0 + item.level * 20.0,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment
            .center, // Cambiado de start a center para mejor alineación
        children: [
          Transform.scale(
            scale: 0.8, // Reduce el tamaño del checkbox al 80%
            child: SizedBox(
              width: 18.0, // Aumentado ligeramente para mejor proporción
              height: 18.0,
              child: Checkbox(
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                visualDensity: VisualDensity.compact,
                value: checked,
                onChanged: (bool? value) {
                  setState(() {
                    _checkboxStates[item.headerValue] = value ?? false;
                  });
                },
              ),
            ),
          ),
          SizedBox(
              width:
                  8.0), // Aumentado de 0 a 8.0 para más espacio entre checkbox y texto
          Expanded(
            child: Text.rich(
              TextSpan(children: _buildTextSpans(item)),
            ),
          ),
        ],
      ),
    );
  }

  String _getBulletText(int level) {
    switch (level) {
      case 0:
        return '•'; // Círculo lleno para el nivel 1
      case 1:
        return '◦'; // Círculo vacío para el nivel 2
      case 2:
        return '⇨'; // Flecha hacia la derecha con línea horizontal para el nivel 3
      default:
        return '•'; // Puedes cambiar esto para otros niveles
    }
  }

  String safeDecodeUri(String uri) {
    try {
      // Verificar si la URI ya está decodificada
      final decodedUri = Uri.decodeComponent(uri);
      if (Uri.encodeComponent(decodedUri) == uri) {
        return decodedUri;
      }
      return uri;
    } catch (e) {
      // Manejar el error de manera silenciosa
      return uri; // Devuelve la URI original si hay un error
    }
  }

  bool isValidUri(String uri) {
    try {
      Uri.parse(uri);
      return true;
    } catch (e) {
      return false;
    }
  }

  List<TextSpan> _buildTextSpans(Item item) {
    final List<TextSpan> spans = [];
    final RegExp boldRegExp = RegExp(r'\*\*(.*?)\*\*');
    final RegExp italicRegExp = RegExp(r'(?<!\*)\*(?!\*)(.*?)\*(?!\*)');
    final RegExp linkRegExp = RegExp(r'\[(.*?)\]\((.*?)\)');
    final RegExp imageRegExp =
        RegExp(r'!\[(.*?)\]\((.*?)\)'); // Expresión regular para imágenes

    String text = item.headerValue +
        (item.additionalContent.isNotEmpty ? ' ${item.additionalContent}' : '');

    TextStyle baseStyle = hStyles[item.tag] ??
        const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.normal,
            color: Colors.black,
            fontFamily: 'TextStyle');

    while (text.isNotEmpty) {
      final boldMatch = boldRegExp.firstMatch(text);
      final italicMatch = italicRegExp.firstMatch(text);
      final linkMatch = linkRegExp.firstMatch(text);
      final imageMatch = imageRegExp.firstMatch(text); // Detectar imágenes

      if (boldMatch != null &&
          (italicMatch == null || boldMatch.start < italicMatch.start) &&
          (linkMatch == null || boldMatch.start < linkMatch.start) &&
          (imageMatch == null || boldMatch.start < imageMatch.start)) {
        if (boldMatch.start > 0) {
          spans.add(TextSpan(
              text: text.substring(0, boldMatch.start), style: baseStyle));
        }
        String boldText = boldMatch.group(1)!;
        if (_searchQuery.isNotEmpty) {
          final searchRegExp = RegExp(_searchQuery, caseSensitive: false);
          final searchMatch = searchRegExp.firstMatch(boldText);
          if (searchMatch != null) {
            if (searchMatch.start > 0) {
              spans.add(TextSpan(
                  text: boldText.substring(0, searchMatch.start),
                  style: hStyles['strong']));
            }
            spans.add(TextSpan(
                text: searchMatch.group(0),
                style: hStyles['strong']!.copyWith(
                    backgroundColor: _matchKeys.isNotEmpty &&
                            _matchKeys[_currentMatchIndex] == item.key
                        ? Colors.orange
                        : Colors.yellow)));
            spans.add(TextSpan(
                text: boldText.substring(searchMatch.end),
                style: hStyles['strong']));
            if (!_matchKeys.contains(item.key)) {
              _matchCountNotifier.value++;
              _matchKeys.add(item.key);
            }
          } else {
            spans.add(TextSpan(text: boldText, style: hStyles['strong']));
          }
        } else {
          spans.add(TextSpan(text: boldText, style: hStyles['strong']));
        }
        text = text.substring(boldMatch.end);
      } else if (italicMatch != null &&
          (linkMatch == null || italicMatch.start < linkMatch.start) &&
          (imageMatch == null || italicMatch.start < imageMatch.start)) {
        if (italicMatch.start > 0) {
          spans.add(TextSpan(
              text: text.substring(0, italicMatch.start), style: baseStyle));
        }
        String italicText = italicMatch.group(1)!;
        if (_searchQuery.isNotEmpty) {
          final searchRegExp = RegExp(_searchQuery, caseSensitive: false);
          final searchMatch = searchRegExp.firstMatch(italicText);
          if (searchMatch != null) {
            if (searchMatch.start > 0) {
              spans.add(TextSpan(
                  text: italicText.substring(0, searchMatch.start),
                  style: hStyles['em']));
            }
            spans.add(TextSpan(
                text: searchMatch.group(0),
                style: hStyles['em']!.copyWith(
                    backgroundColor: _matchKeys.isNotEmpty &&
                            _matchKeys[_currentMatchIndex] == item.key
                        ? Colors.orange
                        : Colors.yellow)));
            spans.add(TextSpan(
                text: italicText.substring(searchMatch.end),
                style: hStyles['em']));
            if (!_matchKeys.contains(item.key)) {
              _matchCountNotifier.value++;
              _matchKeys.add(item.key);
            }
          } else {
            spans.add(TextSpan(text: italicText, style: hStyles['em']));
          }
        } else {
          spans.add(TextSpan(text: italicText, style: hStyles['em']));
        }
        text = text.substring(italicMatch.end);
      } else if (linkMatch != null &&
          (imageMatch == null || linkMatch.start < imageMatch.start)) {
        if (linkMatch.start > 0) {
          spans.add(TextSpan(
              text: text.substring(0, linkMatch.start), style: baseStyle));
        }
        String linkText = linkMatch.group(1)!;
        if (_searchQuery.isNotEmpty) {
          final searchRegExp = RegExp(_searchQuery, caseSensitive: false);
          final searchMatch = searchRegExp.firstMatch(linkText);
          if (searchMatch != null) {
            if (searchMatch.start > 0) {
              spans.add(TextSpan(
                  text: linkText.substring(0, searchMatch.start),
                  style: hStyles['a']));
            }
            spans.add(TextSpan(
                text: searchMatch.group(0),
                style: hStyles['a']!.copyWith(
                    backgroundColor: _matchKeys.isNotEmpty &&
                            _matchKeys[_currentMatchIndex] == item.key
                        ? Colors.orange
                        : Colors.yellow)));
            spans.add(TextSpan(
                text: linkText.substring(searchMatch.end),
                style: hStyles['a']));
            if (!_matchKeys.contains(item.key)) {
              _matchCountNotifier.value++;
              _matchKeys.add(item.key);
            }
          } else {
            spans.add(TextSpan(
              text: linkText,
              style: hStyles['a'],
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  final link = linkMatch.group(2);
                  if (link != null && link.startsWith('#')) {
                    link.substring(1);
                  }
                },
            ));
          }
        } else {
          spans.add(TextSpan(
            text: linkText,
            style: hStyles['a'],
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                final link = linkMatch.group(2);
                if (link != null && link.startsWith('#')) {
                  final encodedLink = link.substring(1);
                  safeDecodeUri(encodedLink);
                }
              },
          ));
        }
        text = text.substring(linkMatch.end);
      } else if (imageMatch != null) {
        if (imageMatch.start > 0) {
          spans.add(TextSpan(
              text: text.substring(0, imageMatch.start), style: baseStyle));
        }
        String altText = imageMatch.group(1)!;
        String imageName = imageMatch.group(2)!;
        if (_searchQuery.isNotEmpty) {
          final searchRegExp = RegExp(_searchQuery, caseSensitive: false);
          final searchMatch = searchRegExp.firstMatch(altText);
          if (searchMatch != null) {
            if (searchMatch.start > 0) {
              spans.add(TextSpan(
                  text: altText.substring(0, searchMatch.start),
                  style: hStyles['imageLink'] ?? hStyles['a']));
            }
            spans.add(TextSpan(
                text: searchMatch.group(0),
                style: (hStyles['imageLink'] ?? hStyles['a'])!.copyWith(
                    backgroundColor: _matchKeys.isNotEmpty &&
                            _matchKeys[_currentMatchIndex] == item.key
                        ? Colors.orange
                        : Colors.yellow)));
            spans.add(TextSpan(
                text: altText.substring(searchMatch.end),
                style: hStyles['imageLink'] ?? hStyles['a']));
            if (!_matchKeys.contains(item.key)) {
              _matchCountNotifier.value++;
              _matchKeys.add(item.key);
            }
          } else {
            spans.add(TextSpan(
                text: altText, style: hStyles['imageLink'] ?? hStyles['a']));
          }
        } else {
          spans.add(TextSpan(
            text: altText,
            style: hStyles['imageLink'] ?? hStyles['a'],
            recognizer: TapGestureRecognizer()
              ..onTap = () async {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => ImagenView(
                      imageName: imageName,
                      leccionId: widget.leccionId,
                    ),
                  ),
                );
              },
          ));
        }
        text = text.substring(imageMatch.end);
      } else {
        if (_searchQuery.isNotEmpty) {
          final searchRegExp = RegExp(_searchQuery, caseSensitive: false);
          final searchMatch = searchRegExp.firstMatch(text);
          if (searchMatch != null) {
            if (searchMatch.start > 0) {
              spans.add(TextSpan(
                  text: text.substring(0, searchMatch.start),
                  style: baseStyle));
            }
            spans.add(TextSpan(
                text: searchMatch.group(0),
                style: baseStyle.copyWith(
                    backgroundColor: _matchKeys.isNotEmpty &&
                            _matchKeys[_currentMatchIndex] == item.key
                        ? Colors.orange
                        : Colors.yellow)));
            text = text.substring(searchMatch.end);
            if (!_matchKeys.contains(item.key)) {
              _matchCountNotifier.value++;
              _matchKeys.add(item.key);
            }
            continue;
          }
        }
        spans.add(TextSpan(text: text, style: baseStyle));
        text = '';
      }
    }

    return spans;
  }

  void _scrollToNextMatch() {
    if (_matchKeys.isNotEmpty) {
      setState(() {
        _currentMatchIndex = (_currentMatchIndex + 1) % _matchKeys.length;
        _currentMatchNotifier.value =
            _currentMatchIndex + 1; // Actualizar coincidencia actual
      });
      _scrollToCurrentMatch();
    }
  }

  void _scrollToPreviousMatch() {
    if (_matchKeys.isNotEmpty) {
      setState(() {
        _currentMatchIndex =
            (_currentMatchIndex - 1 + _matchKeys.length) % _matchKeys.length;
        _currentMatchNotifier.value =
            _currentMatchIndex + 1; // Actualizar coincidencia actual
      });
      _scrollToCurrentMatch();
    }
  }

  void _scrollToCurrentMatch() {
    if (_matchKeys.isNotEmpty) {
      final key = _matchKeys[_currentMatchIndex];
      final context = key.currentContext;
      if (context != null) {
        Scrollable.ensureVisible(context,
            duration: const Duration(milliseconds: 500));
      }
    }
  }

  Widget _buildTable(String tableContent) {
    final lines = tableContent.trim().split('\n');
    List<List<String>> rows = [];

    String currentCellContent = '';
    bool collectingListItems = false;

    for (int i = 0; i < lines.length; i++) {
      String line = lines[i];

      if (line.trim().startsWith('|') && !line.trim().startsWith('|-')) {
        if (collectingListItems && currentCellContent.isNotEmpty) {
          // Finalizar la celda con contenido de lista
          rows.add([currentCellContent.trim()]);
          currentCellContent = '';
          collectingListItems = false;
        }

        // Es una fila normal de tabla
        final cells = line
            .split('|')
            .map((cell) => cell.trim())
            .where((cell) => cell.isNotEmpty)
            .toList();
        rows.add(cells);
      } else if (line.trim().startsWith('-') ||
          line.trim().startsWith('*') ||
          line.trim().startsWith('•')) {
        // Es una línea de lista
        if (!collectingListItems) {
          collectingListItems = true;
          currentCellContent = line;
        } else {
          currentCellContent += '\n$line';
        }
      } else if (line.trim().startsWith('|-')) {
        // Es una línea separadora, ignorarla
        continue;
      }
    }

    // Si quedó contenido de lista pendiente, agregarlo
    if (collectingListItems && currentCellContent.isNotEmpty) {
      rows.add([currentCellContent.trim()]);
    }

    // Encontrar el número máximo de columnas
    int maxColumns = 0;
    for (var row in rows) {
      if (row.length > maxColumns) {
        maxColumns = row.length;
      }
    }

    // Normalizar todas las filas para que tengan el mismo número de columnas
    for (int i = 0; i < rows.length; i++) {
      while (rows[i].length < maxColumns) {
        rows[i].add(''); // Agregar celdas vacías
      }
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        scrollDirection: Axis.vertical,
        child: SelectionArea(
          // Asegurarse de que el Table esté envuelto en un SelectionArea
          child: ClipRRect(
            borderRadius: BorderRadius.circular(
                15.0), // Ajusta el radio de las esquinas aquí
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white, // Ajusta el color de fondo aquí
                borderRadius: BorderRadius.circular(15.0),
                border: Border.all(
                    color: Colors.black,
                    width: 2.0), // Color y grosor del borde de la tabla
              ),
              child: Table(
                border: TableBorder(
                  horizontalInside: BorderSide(color: Colors.black, width: 2.0),
                  verticalInside: BorderSide(color: Colors.black, width: 2.0),
                  top: BorderSide.none,
                  bottom: BorderSide.none,
                  left: BorderSide.none,
                  right: BorderSide.none,
                ),
                columnWidths: const <int, TableColumnWidth>{
                  0: IntrinsicColumnWidth(), // Ajusta las columnas de manera dinámica
                  1: IntrinsicColumnWidth(),
                  2: IntrinsicColumnWidth(),
                  3: IntrinsicColumnWidth(),
                  4: IntrinsicColumnWidth(),
                  5: IntrinsicColumnWidth(),
                  6: IntrinsicColumnWidth(),
                },
                defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                children: rows.map((row) {
                  return TableRow(
                    children: row.map((cell) {
                      return Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Container(
                          constraints: BoxConstraints(
                              maxWidth: 200), // Ajusta el ancho máximo aquí
                          child: _buildTableCellContent(cell),
                        ),
                      );
                    }).toList(),
                  );
                }).toList(),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTableCellContent(String cellContent) {
    // Detectar si la celda contiene un box (:::box)
    if (cellContent.trim().startsWith(':::box')) {
      return _buildBoxWidget(cellContent);
    }

    // Detectar si la celda contiene listas (líneas que empiezan con -, *, o •)
    final lines = cellContent.split('\n');
    bool hasListItems = lines.any((line) =>
        line.trim().startsWith('-') ||
        line.trim().startsWith('*') ||
        line.trim().startsWith('•'));

    if (hasListItems) {
      // Construir una columna con elementos de lista
      List<Widget> listWidgets = [];

      for (String line in lines) {
        String trimmedLine = line.trim();
        if (trimmedLine.isEmpty) continue;

        if (trimmedLine.startsWith('-') ||
            trimmedLine.startsWith('*') ||
            trimmedLine.startsWith('•')) {
          // Es un elemento de lista
          String listText =
              trimmedLine.substring(1).trim(); // Remover el marcador de lista
          listWidgets.add(
            Padding(
              padding: const EdgeInsets.only(bottom: 4.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '•', // Usar siempre bullet point
                    style: hStyles['listBullet'] ?? TextStyle(fontSize: 14),
                  ),
                  const SizedBox(width: 0.0),
                  Expanded(
                    child: RichText(
                      text: TextSpan(
                        children: _buildTextSpans(Item(
                          headerValue: listText,
                          tag: 'tableBody',
                          key: GlobalKey(),
                        )),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        } else {
          // Es texto normal
          listWidgets.add(
            Padding(
              padding: const EdgeInsets.only(bottom: 4.0),
              child: RichText(
                text: TextSpan(
                  children: _buildTextSpans(Item(
                    headerValue: trimmedLine,
                    tag: 'tableBody',
                    key: GlobalKey(),
                  )),
                ),
              ),
            ),
          );
        }
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: listWidgets,
      );
    } else {
      // Contenido normal sin listas
      return RichText(
        text: TextSpan(
          children: _buildTextSpans(Item(
            headerValue: cellContent,
            tag: 'tableBody',
            key: GlobalKey(),
          )),
        ),
      );
    }
  }

  Widget _buildBoxWidget(String boxContent) {
    // Parsear el contenido del box
    final lines = boxContent.trim().split('\n');
    String title = '';
    List<String> listItems = [];

    for (int i = 0; i < lines.length; i++) {
      String line = lines[i].trim();

      if (line.startsWith(':::box ')) {
        // Extraer el título
        title = line.substring(7).trim(); // Remover ':::box '
      } else if (line == ':::') {
        // Fin del box, terminar
        break;
      } else if (line.startsWith('-') ||
          line.startsWith('*') ||
          line.startsWith('•')) {
        // Es un elemento de lista
        listItems.add(line.substring(1).trim()); // Remover el marcador
      } else if (line.isNotEmpty) {
        // Texto adicional (puede ser parte del título o descripción)
        if (title.isEmpty) {
          title = line;
        }
      }
    }

    return Container(
      margin: EdgeInsets.all(4.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[400]!, width: 1.5),
        borderRadius: BorderRadius.circular(8.0),
        color: Colors.grey[50],
      ),
      child: Padding(
        padding: EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Título del box
            if (title.isNotEmpty)
              Container(
                padding: EdgeInsets.only(bottom: 8.0),
                child: Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
              ),
            // Lista de elementos
            ...listItems.map((item) => Padding(
                  padding: EdgeInsets.only(bottom: 4.0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '•',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.black87,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(width: 6.0),
                      Expanded(
                        child: Text(
                          item,
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildCuadro(String cuadroId) {
    return FutureBuilder<Map<String, dynamic>?>(
      future: _loadCuadroData(cuadroId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return SizedBox(
            height: 100,
            child: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (snapshot.hasError) {
          return Container(
            key: keys['[CUADRO:$cuadroId]'], // AGREGAR LA CLAVE AQUÍ
            padding: EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.red),
            ),
            child: Text(
              'Error al cargar cuadro: ${snapshot.error}',
              style: TextStyle(color: Colors.red),
            ),
          );
        }

        final cuadroData = snapshot.data;
        if (cuadroData == null) {
          return Container(
            key: keys['[CUADRO:$cuadroId]'], // AGREGAR LA CLAVE AQUÍ
            padding: EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.orange),
            ),
            child: Text(
              'Cuadro no encontrado: ID $cuadroId',
              style: TextStyle(color: Colors.orange),
            ),
          );
        }

        return Container(
          key: keys['[CUADRO:$cuadroId]'], // AGREGAR LA CLAVE AQUÍ
          child: _renderCuadroFromJson(cuadroData),
        );
      },
    );
  }

  Future<Map<String, dynamic>?> _loadCuadroData(String cuadroId) async {
    try {
      print('🔍 Intentando cargar cuadro con ID: $cuadroId');
      final response = await Supabase.instance.client
          .from('cuadros')
          .select('titulo, json_data')
          .eq('cuadro_id', int.parse(cuadroId))
          .maybeSingle();

      print('📦 Respuesta de Supabase: $response');
      return response;
    } catch (e) {
      print('❌ Error cargando cuadro $cuadroId: $e');
      _logger.e('Error cargando cuadro $cuadroId: $e');
      return null;
    }
  }

  Widget _renderCuadroFromJson(Map<String, dynamic> cuadroData) {
    final titulo = cuadroData['titulo'] ?? '';
    final jsonData = cuadroData['json_data'] as Map<String, dynamic>;

    final columnas = jsonData['columnas'] ?? 1;
    final filas = jsonData['filas'] ?? 1;
    final celdas = jsonData['celdas'] as List<dynamic>? ?? [];
    final colorValue = jsonData['color'] as int?;

    colorValue != null ? Color(colorValue) : Colors.grey[600]!;

    final fontSize = (jsonData['fontSize'] as num?)?.toDouble() ?? 14.0;

    // Obtener anchos de columnas desde columnasData o anchosColumnas
    List<double> anchosColumnas = [];
    if (jsonData['columnasData'] != null) {
      final columnasData = jsonData['columnasData'] as List<dynamic>;
      anchosColumnas = columnasData.map((col) {
        final ancho = col['ancho'];
        if (ancho is int) {
          return ancho.toDouble();
        } else if (ancho is double) {
          return ancho;
        } else {
          return 200.0; // Valor por defecto si no es numérico
        }
      }).toList();
    } else if (jsonData['anchosColumnas'] != null) {
      anchosColumnas = List<double>.from(jsonData['anchosColumnas']);
    }

    // Crear matriz de celdas
    List<List<Map<String, dynamic>>> matriz = List.generate(
      filas,
      (fila) => List.generate(
        columnas,
        (columna) => {
          'titulo': '',
          'contenido': [],
          'colorFondo': Colors.white.toARGB32(), // Color por defecto blanco
        },
      ),
    );

    // Llenar la matriz con los datos
    for (var celda in celdas) {
      final fila = celda['fila'] ?? 0;
      final columna = celda['columna'] ?? 0;
      final colorFondoCelda = celda['colorFondo'];

      if (fila < filas && columna < columnas) {
        matriz[fila][columna] = {
          'titulo': celda['titulo'] ?? '',
          'contenido': celda['contenido'] ?? [],
          'colorFondo': colorFondoCelda ?? Colors.white.toARGB32(),
        };
      }
    }

    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.0),
      decoration: BoxDecoration(
        border:
            Border.all(color: Colors.black, width: 1.0), // Borde simple negro
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Título del cuadro
          if (titulo.isNotEmpty)
            Container(
              width: anchosColumnas.isNotEmpty
                  ? anchosColumnas.reduce((a, b) => a + b)
                  : double.infinity,
              padding: EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 8.0),
              decoration: BoxDecoration(
                color: Colors.grey[200], // Fondo gris simple
                border: Border(
                  bottom: BorderSide(
                      color: Colors.black, width: 1.0), // Solo borde inferior
                ),
              ),
              child: Text(
                titulo,
                style: TextStyle(
                  fontSize: fontSize,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
                textAlign: TextAlign.left,
              ),
            ),
          // Tabla del cuadro
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Column(
              children: matriz.map((fila) {
                return IntrinsicHeight(
                  child: Row(
                    children: fila.asMap().entries.map((entry) {
                      final columna = entry.key;
                      final celda = entry.value;
                      final titulo = celda['titulo'] as String? ?? '';
                      final contenido =
                          celda['contenido'] as List<dynamic>? ?? [];

                      // Obtener color de la celda individual si existe
                      Color colorCelda =
                          Colors.white; // Color por defecto blanco
                      if (celda['colorFondo'] != null) {
                        colorCelda = Color(celda['colorFondo'] as int);
                      }

                      // Obtener ancho de columna
                      double anchoColumna = 200.0; // Ancho por defecto
                      if (anchosColumnas.isNotEmpty &&
                          columna < anchosColumnas.length) {
                        anchoColumna = anchosColumnas[columna];
                      }

                      return Container(
                        width: anchoColumna,
                        padding: EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 8.0),
                        decoration: BoxDecoration(
                          color: colorCelda, // Color sólido sin transparencia
                          border: Border.all(
                              color: Colors.black,
                              width: 1.0), // Borde negro simple
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            // Mostrar título si existe
                            if (titulo.isNotEmpty)
                              Padding(
                                padding: EdgeInsets.only(bottom: 4.0),
                                child: Text(
                                  titulo,
                                  style: TextStyle(
                                    fontSize: fontSize,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black,
                                  ),
                                ),
                              ),

                            ..._buildContenidoJerarquicoLeccion(contenido,
                                fontSize: fontSize),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildContenidoJerarquicoLeccion(List<dynamic> items,
      {required double fontSize, int level = 0}) {
    List<Widget> widgets = [];
    final double indent = 16.0 * level;

    for (var item in items) {
      if (item is String) {
        if (item.trim().isEmpty) continue;

        const bullets = ['•', '◦', '-'];
        final bullet =
            bullets[level < bullets.length ? level : bullets.length - 1];

        widgets.add(
          Padding(
            padding: EdgeInsets.only(left: indent, bottom: 1.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  bullet,
                  style: TextStyle(
                    fontSize: fontSize,
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                    height: 1.0,
                  ),
                ),
                SizedBox(width: 4.0),
                Expanded(
                  child: Text(
                    item,
                    style: TextStyle(
                      fontSize: fontSize,
                      color: Colors.black,
                      height: 1.0,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      } else if (item is Map<String, dynamic>) {
        final titulo = item['titulo'] as String? ?? '';
        final contenido = item['contenido'] as List<dynamic>? ?? [];

        if (titulo.isNotEmpty) {
          final bool alreadyHasBullet = titulo.startsWith('•') ||
              titulo.startsWith('◦') ||
              titulo.startsWith('-') ||
              titulo.startsWith('- ');

          if (alreadyHasBullet) {
            String cleanTitle = titulo;
            if (titulo.startsWith('• ')) {
              cleanTitle = titulo.substring(2);
            } else if (titulo.startsWith('◦ ')) {
              cleanTitle = titulo.substring(2);
            } else if (titulo.startsWith('- ')) {
              cleanTitle = titulo.substring(2);
            }

            const bullets = ['•', '◦', '-'];
            final bullet =
                bullets[level < bullets.length ? level : bullets.length - 1];

            widgets.add(
              Padding(
                padding: EdgeInsets.only(left: indent, bottom: 1.0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      bullet,
                      style: TextStyle(
                        fontSize: fontSize,
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                        height: 1.0,
                      ),
                    ),
                    SizedBox(width: 4.0),
                    Expanded(
                      child: Text(
                        cleanTitle,
                        style: TextStyle(
                          fontSize: fontSize,
                          color: Colors.black,
                          height: 1.0,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          } else {
            widgets.add(
              Padding(
                padding: EdgeInsets.only(left: indent, bottom: 2.0),
                child: Text(
                  titulo,
                  style: TextStyle(
                    fontSize: fontSize,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                    height: 1.0,
                  ),
                ),
              ),
            );
          }
        }

        widgets.addAll(_buildContenidoJerarquicoLeccion(contenido,
            fontSize: fontSize, level: level + 1));
      }
    }
    return widgets;
  }
}
