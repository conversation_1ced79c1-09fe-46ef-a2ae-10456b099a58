import 'package:flutter/material.dart';
// ignore: unused_import
import 'package:flutter/foundation.dart'; // Importar foundation.dart

class DrawerItem {
  final String title;
  final bool hasBulletIcon;
  final int index;
  final String fullText;
  final String originalTag;
  final int level;
  final GlobalKey? key;

  DrawerItem(this.title, this.hasBulletIcon, this.index, this.level,
      [this.fullText = '', this.originalTag = '', this.key]);
}

class LeccionDrawer extends StatelessWidget {
  final String htmlContent;
  final GlobalKey<ScaffoldState> scaffoldKey;
  final Map<String, GlobalKey> keys;

  const LeccionDrawer({
    super.key,
    required this.htmlContent,
    required this.scaffoldKey,
    required this.keys,
  });

  List<DrawerItem> _generateDrawerTitles(String htmlContent) {
    final List<DrawerItem> titles = [];

    // Dividir el contenido HTML en líneas
    final lines = htmlContent.split('\n');

    // Expresiones regulares para buscar títulos y elementos de lista
    final RegExp headerRegExp = RegExp(r'^\s*(#{1,6})\s*(.*)');
    final RegExp listItemRegExp = RegExp(r'^(\s*)-\s*(.*?)\s*\*\*(.*?)\*\*');

    for (final line in lines) {
      // Buscar títulos con #
      final headerMatch = headerRegExp.firstMatch(line);
      if (headerMatch != null) {
        final level = headerMatch.group(1)!.length;
        final title = headerMatch.group(2)?.trim() ?? '';
        if (title.isNotEmpty) {
          final key = keys[line.trim()]; // Usar la línea completa para la clave
          titles.add(DrawerItem(
              title, false, titles.length, level, '', 'header', key));
        }
      }

      // Buscar elementos de lista con guion y texto entre **
      final listItemMatch = listItemRegExp.firstMatch(line);
      if (listItemMatch != null) {
        final spaces = listItemMatch.group(1)?.length ?? 0;
        final title =
            listItemMatch.group(3)?.trim() ?? ''; // Usar el texto entre **
        if (title.isNotEmpty) {
          int level =
              (spaces / 2).floor(); // Calcular el nivel basado en espacios
          final key = keys[line.trim()]; // Usar la línea completa para la clave
          titles.add(DrawerItem(
              title, true, titles.length, level, '', 'listItem', key));
        }
      }
    }

    return titles;
  }

  @override
  Widget build(BuildContext context) {
    // Generar los drawerItems dentro del widget
    List<DrawerItem> drawerItems = _generateDrawerTitles(htmlContent);

    return Drawer(
      child: Container(
        color: const Color.fromARGB(255, 32, 46, 53),
        child: SafeArea(
          child: ListView(
            padding: const EdgeInsets.only(top: 20.0),
            children: <Widget>[
              ...drawerItems.asMap().entries.expand((entry) {
                var item = entry.value;

                return [
                  // Agregar un Divider antes de cada "header" que no sea h2 o h3
                  if (entry.key > 0 &&
                      drawerItems[entry.key - 1].originalTag == 'header' &&
                      drawerItems[entry.key - 1].level == 1)
                    const Divider(height: 1.0),

                  // Agregar un Divider después de cada "header" que no sea h2 o h3
                  if (item.originalTag == 'header' && item.level == 1)
                    const Divider(height: 1.0),

                  Material(
                    color: const Color.fromARGB(0, 125, 157, 176),
                    child: InkWell(
                      splashColor: const Color.fromARGB(255, 105, 146, 179)
                          .withAlpha(30),
                      onTap: () {
                        final key = item.key;
                        if (key?.currentContext != null) {
                          Scrollable.ensureVisible(
                            key!.currentContext!,
                            duration: const Duration(milliseconds: 250),
                            curve: Curves.easeInOut,
                          );
                        } else {
                          // Handle the case where the key is null
                        }
                        Navigator.pop(context);
                      },
                      child: Container(
                        padding: EdgeInsets.only(
                            left: item.originalTag == 'listItem'
                                ? 20.0 + item.level * 10.0
                                : 10.0), // Ajustar el padding basado en el nivel solo para listItem
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: Padding(
                            padding: EdgeInsets.zero, // No padding for headers
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    if (item.hasBulletIcon)
                                      Text(
                                        _getBulletText(item.level),
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 16,
                                        ),
                                      ),
                                    const SizedBox(width: 4.0),
                                    Expanded(
                                      child: Text(
                                        item.title,
                                        maxLines: 3,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                // Comentar o eliminar esta sección para ocultar los GlobalKey en rojo
                                // if (kDebugMode)
                                //   Text(
                                //     'Key: ${item.key}',
                                //     style: const TextStyle(
                                //         color: Colors.red, fontSize: 10),
                                //   ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ];
              }),
            ],
          ),
        ),
      ),
    );
  }

  String _getBulletText(int level) {
    switch (level) {
      case 0:
        return '•'; // Círculo lleno para el nivel 1
      case 1:
        return '◦'; // Círculo vacío para el nivel 2
      case 2:
        return '⇨'; // Flecha hacia la derecha con línea horizontal para el nivel 3
      default:
        return '•'; // Puedes cambiar esto para otros niveles
    }
  }
}
