import 'package:flutter/material.dart';

class Item {
  String headerValue;
  String tag;
  List<Item> children;
  BuildContext? context;
  bool isExpanded;
  bool hasBulletIcon;
  GlobalKey key;
  String additionalContent;
  int level;

  Item({
    required this.headerValue,
    required this.tag,
    this.context,
    this.isExpanded = false,
    this.children = const [],
    this.hasBulletIcon = false,
    required this.key,
    this.additionalContent = '',
    this.level = 0,
  });
}

class CheckboxListBuilder extends StatefulWidget {
  final List<Item> items;
  final Map<String, bool> checkboxStates;
  final Function(String, bool) onChanged;

  const CheckboxListBuilder({
    super.key,
    required this.items,
    required this.checkboxStates,
    required this.onChanged,
  });

  @override
  CheckboxListBuilderState createState() => CheckboxListBuilderState();
}

class CheckboxListBuilderState extends State<CheckboxListBuilder> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widget.items.map((item) {
        bool checked = widget.checkboxStates[item.headerValue] ?? false;
        return Padding(
          padding: EdgeInsets.symmetric(
            vertical: 1.0,
            horizontal: 16.0 + item.level * 20.0,
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: 14.0,
                height: 14.0,
                child: Checkbox(
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  visualDensity: VisualDensity.compact,
                  value: checked,
                  onChanged: (bool? value) {
                    widget.onChanged(item.headerValue, value ?? false);
                  },
                ),
              ),
              Expanded(
                child: Text(item.headerValue),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}

class CuadroBuilder extends StatelessWidget {
  final Map<String, dynamic> cuadroData;
  final double fontSize;

  const CuadroBuilder({
    super.key,
    required this.cuadroData,
    this.fontSize = 14.0,
  });

  @override
  Widget build(BuildContext context) {
    final titulo = cuadroData['titulo'] ?? '';
    final jsonData = cuadroData['json_data'] as Map<String, dynamic>;

    final columnas = jsonData['columnas'] ?? 1;
    final filas = jsonData['filas'] ?? 1;
    final celdas = jsonData['celdas'] as List<dynamic>? ?? [];
    final colorValue = jsonData['color'] as int?;

    colorValue != null ? Color(colorValue) : Colors.grey[600]!;

    // Obtener anchos de columnas desde columnasData o anchosColumnas
    List<double> anchosColumnas = [];
    if (jsonData['columnasData'] != null) {
      final columnasData = jsonData['columnasData'] as List<dynamic>;
      anchosColumnas = columnasData.map((col) {
        final ancho = col['ancho'];
        if (ancho is int) {
          return ancho.toDouble();
        } else if (ancho is double) {
          return ancho;
        } else {
          return 200.0; // Valor por defecto si no es numérico
        }
      }).toList();
    } else if (jsonData['anchosColumnas'] != null) {
      anchosColumnas = List<double>.from(jsonData['anchosColumnas']);
    }

    // Crear matriz de celdas
    List<List<Map<String, dynamic>>> matriz = List.generate(
      filas,
      (fila) => List.generate(
        columnas,
        (columna) => {
          'titulo': '',
          'contenido': [],
          'colorFondo': Colors.white, // Color por defecto blanco
        },
      ),
    );

    // Llenar la matriz con los datos
    for (var celda in celdas) {
      final fila = celda['fila'] ?? 0;
      final columna = celda['columna'] ?? 0;
      final colorFondoCelda = celda['colorFondo'];

      if (fila < filas && columna < columnas) {
        matriz[fila][columna] = {
          'titulo': celda['titulo'] ?? '',
          'contenido': celda['contenido'] ?? [],
          'colorFondo': colorFondoCelda ?? Colors.white,
        };
      }
    }

    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.black, width: 1.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (titulo.isNotEmpty)
            Container(
              width: anchosColumnas.isNotEmpty
                  ? anchosColumnas.reduce((a, b) => a + b)
                  : double.infinity,
              padding: EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 8.0),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                border: Border(
                  bottom: BorderSide(color: Colors.black, width: 1.0),
                ),
              ),
              child: Text(
                titulo,
                style: TextStyle(
                  fontSize: fontSize,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
                textAlign: TextAlign.left,
              ),
            ),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Column(
              children: matriz.map((fila) {
                return IntrinsicHeight(
                  child: Row(
                    children: fila.asMap().entries.map((entry) {
                      final columna = entry.key;
                      final celda = entry.value;
                      final titulo = celda['titulo'] as String? ?? '';
                      final contenido =
                          celda['contenido'] as List<dynamic>? ?? [];

                      Color colorCelda = Colors.white;
                      if (celda['colorFondo'] != null) {
                        colorCelda = Color(celda['colorFondo'] as int);
                      }

                      double anchoColumna = 200.0;
                      if (anchosColumnas.isNotEmpty &&
                          columna < anchosColumnas.length) {
                        anchoColumna = anchosColumnas[columna];
                      }

                      return Container(
                        width: anchoColumna,
                        padding: EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 8.0),
                        decoration: BoxDecoration(
                          color: colorCelda,
                          border: Border.all(color: Colors.black, width: 1.0),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            if (titulo.isNotEmpty)
                              Padding(
                                padding: EdgeInsets.only(bottom: 4.0),
                                child: Text(
                                  titulo,
                                  style: TextStyle(
                                    fontSize: fontSize,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black,
                                  ),
                                ),
                              ),
                            // You can add recursive content rendering here if needed
                            // For now, just display content as text
                            ...contenido.map((contentItem) {
                              if (contentItem is String) {
                                return Text(contentItem);
                              } else {
                                return SizedBox.shrink();
                              }
                            }),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}
