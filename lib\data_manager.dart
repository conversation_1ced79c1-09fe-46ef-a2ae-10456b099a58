import 'package:flutter_cache_manager/flutter_cache_manager.dart';

class DataManager {
  Future<void> downloadData() async {
    // Aquí es donde llamarías a tu función para descargar los datos de Supabase
    // y almacenarlos en el caché.
    // Por ejemplo:
    await DefaultCacheManager().getSingleFile('https://supabase.io');
    // ...
  }

  Future<String> useData() async {
    var file = await DefaultCacheManager().getSingleFile('https://supabase.io');
    if (await file.exists()) {
      var data = await file.readAsString();
      return data;
    } else {
      // Muestra un mensaje al usuario para que descargue los datos
      throw Exception('No data available');
    }
  }
}
