name: appmanager
description: "A new Flutter project."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.5.4 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  package_info_plus: ^8.1.1
  shared_preferences: ^2.3.3
  sign_in_with_apple: ^6.1.3
  wakelock_plus: ^1.2.8  
  flutter_html: ^3.0.0-beta.2
  supabase_flutter: ^2.9.1
  supabase: ^2.8.0
  flutter_widget_from_html: ^0.16.0
  supabase_auth_ui: ^0.5.1
  flutter_dotenv: ^5.1.0
  intl: ^0.20.2
  logging: ^1.2.0
  flutter_cache_manager: ^3.4.1
  path: ^1.9.0
  sqflite_common_ffi: ^2.3.6
  local_auth: ^2.3.0
  permission_handler: ^12.0.0+1
  flutter_markdown: ^0.7.4+2
  markdown: ^7.2.2
  markdown_editor_plus: ^0.2.15
  http: ^1.2.2
  cached_network_image: ^3.4.1
  path_provider: ^2.1.4 
  photo_view: ^0.15.0
  image_picker: ^1.1.2
  logger: ^2.6.0
  archive: ^4.0.5
  file_picker: ^10.2.0
  dio: ^5.7.0
  go_router: ^15.2.4
  desktop_drop: ^0.6.0
  cross_file: ^0.3.4+2
  flutter_colorpicker: ^1.1.0
  fl_chart: ^1.0.0
  extended_text_field: ^16.0.2
  crypto: ^3.0.6
  convert: ^3.0.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

flutter:
  assets:
    - supabasekeys.env
    - assets/en_words.txt
    - assets/de_words.txt
    - assets/images/
    - assets/es_words.dic
    - assets/es_Words.aff
    - assets/es_conj.txt
    - assets/terminos_medicos.dic
    - assets/diccionario_expandido.json
    - assets/diccionario_completo_final.json
  uses-material-design: true

  fonts:
    - family: AdventPro
      fonts:
        - asset: assets/fonts/Sans-serif-adventpro/adventpro-regular.ttf
    - family: Aaargh
      fonts:
        - asset: assets/fonts/Aaargh/Aaargh.ttf