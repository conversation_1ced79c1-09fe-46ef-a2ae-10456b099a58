import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class EditarDefinicion extends StatefulWidget {
  final Map<String, dynamic> definicion;

  const EditarDefinicion({super.key, required this.definicion});

  @override
  State<EditarDefinicion> createState() => _EditarDefinicionState();
}

class _EditarDefinicionState extends State<EditarDefinicion> {
  final _nombreController = TextEditingController();
  final _contenidoController = TextEditingController();
  final _supabase = Supabase.instance.client;

  @override
  void initState() {
    super.initState();
    _nombreController.text = widget.definicion['definicion_nombre'];
    _contenidoController.text = widget.definicion['definicion_contenido'];
  }

  Future<void> _actualizarDefinicion() async {
    try {
      final int definicionId = widget.definicion['diccionario_id'] as int;
      final now = DateTime.now();
      final formattedDate =
          "${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}";

      final Map<String, dynamic> updateData = {
        'definicion_nombre': _nombreController.text,
        'definicion_contenido': _contenidoController.text,
        'created_at': formattedDate,
      };

      await _supabase
          .from('diccionario')
          .update(updateData)
          .match({'diccionario_id': definicionId});

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Definición actualizada exitosamente')),
        );
        Navigator.pop(context, true);
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error inesperado: $error')),
        );
      }
    }
  }

  @override
  void dispose() {
    _nombreController.dispose();
    _contenidoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Editar Definición'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            TextField(
              controller: _nombreController,
              decoration: const InputDecoration(
                labelText: 'Definición Nombre',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _contenidoController,
              decoration: const InputDecoration(
                labelText: 'Definición Contenido',
                border: OutlineInputBorder(),
                contentPadding:
                    EdgeInsets.symmetric(vertical: 40.0, horizontal: 12.0),
              ),
              maxLines: 15,
            ),
            const Spacer(),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _actualizarDefinicion,
                child: const Padding(
                  padding: EdgeInsets.symmetric(vertical: 16.0),
                  child: Text('Actualizar'),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
