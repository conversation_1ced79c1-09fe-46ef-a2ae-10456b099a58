import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

class ImagenView extends StatefulWidget {
  final String imageName;
  final int leccionId;

  const ImagenView({
    super.key,
    required this.imageName,
    required this.leccionId,
  });

  @override
  ImagenViewState createState() => ImagenViewState();
}

class ImagenViewState extends State<ImagenView> {
  String? _imagePath;
  List<MarkerPoint> markers = [];
  String imageDescription = '';
  final PhotoViewController photoViewController = PhotoViewController();
  double imageWidth = 0;
  double imageHeight = 0;
  bool isLoading = true;
  bool showTooltips = false; // Controlar la visibilidad de los tooltips
  bool showDescriptionDrawer =
      false; // Controlar visibilidad del drawer de descripción

  @override
  void initState() {
    super.initState();
    _loadImagePath();
  }

  @override
  void dispose() {
    photoViewController.dispose();
    super.dispose();
  }

  Future<void> _loadImagePath() async {
    Directory directory;
    if (Platform.isAndroid) {
      directory = await getApplicationDocumentsDirectory();
      _imagePath = path.join(directory.path, 'images', widget.imageName);
    } else if (Platform.isWindows) {
      directory = Directory(
          'C:/Users/<USER>/AppData/Roaming/com.example/medicappp/images');
      _imagePath = path.join(directory.path, widget.imageName);
    } else {
      directory = await getApplicationDocumentsDirectory();
      _imagePath =
          path.join(directory.path, 'assets', 'images', widget.imageName);
    }
    debugPrint('Image path: $_imagePath');
    await _loadImageData();
    setState(() {
      isLoading = false;
    });
  }

  Future<void> _loadImageData() async {
    try {
      // Buscar imagen_id en la tabla imagenes
      final imageResponse = await Supabase.instance.client
          .from('imagenes')
          .select('imagen_id')
          .eq('imagen_nombre', widget.imageName)
          .maybeSingle();

      if (imageResponse == null) {
        debugPrint('Imagen no encontrada: ${widget.imageName}');
        return;
      }

      final imageId = imageResponse['imagen_id'];
      debugPrint('Image ID: $imageId');

      // Buscar datos en la tabla leccion_imagenes
      final response = await Supabase.instance.client
          .from('leccion_imagenes')
          .select('descripcion_especifica, markers_data')
          .eq('imagen_id', imageId)
          .eq('leccion_id', widget.leccionId)
          .maybeSingle();

      if (response == null) {
        debugPrint('No hay datos de imagen para esta lección');
        return;
      }

      final List<dynamic> markersData = response['markers_data'] ?? [];
      debugPrint('Markers data: $markersData');

      if (markersData.isNotEmpty) {
        // Obtener las dimensiones de la imagen desde el JSONB
        final firstMarker = markersData[0];
        imageWidth = firstMarker['image_width'];
        imageHeight = firstMarker['image_height'];
      } else {
        // Si no hay marcadores, cargar dimensiones desde la imagen
        final decodedImage =
            await decodeImageFromList(await File(_imagePath!).readAsBytes());
        imageWidth = decodedImage.width.toDouble();
        imageHeight = decodedImage.height.toDouble();
      }

      setState(() {
        markers = markersData.map<MarkerPoint>((data) {
          final marker = MarkerPoint(
            position: Offset(data['x_position'], data['y_position']),
            description: data['description'],
            size: data['size'].toDouble(),
            color: Color(data['color']),
            shape: MarkerShape.values[data['shape']],
          );
          debugPrint('Marker position: ${marker.position}');
          return marker;
        }).toList();
        imageDescription = response['descripcion_especifica'] ?? '';
      });
    } catch (e) {
      debugPrint('Error loading image data: $e');
    }
  }

  void _toggleTooltips() {
    setState(() {
      showTooltips = !showTooltips;
    });
  }

  void _toggleDescriptionDrawer() {
    setState(() {
      showDescriptionDrawer = !showDescriptionDrawer;
    });
  }

  void _zoomIn() {
    photoViewController.scale = photoViewController.scale! * 1.2;
  }

  void _zoomOut() {
    photoViewController.scale = photoViewController.scale! / 1.2;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromARGB(255, 103, 197, 231),
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(80),
        child: AppBar(
          automaticallyImplyLeading: false,
          backgroundColor: Colors.black,
          elevation: 0,
          iconTheme: const IconThemeData(color: Colors.white),
          flexibleSpace: SafeArea(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      IconButton(
                        icon: Icon(Icons.arrow_back, color: Colors.white),
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        tooltip: 'Volver',
                      ),
                      SizedBox(width: 8),
                      Text(
                        'Volver',
                        style: TextStyle(color: Colors.white, fontSize: 20),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      IconButton(
                        icon: Icon(Icons.zoom_in, color: Colors.white),
                        onPressed: _zoomIn,
                        tooltip: 'Zoom In',
                      ),
                      IconButton(
                        icon: Icon(Icons.zoom_out, color: Colors.white),
                        onPressed: _zoomOut,
                        tooltip: 'Zoom Out',
                      ),
                      IconButton(
                        icon: Icon(
                            showTooltips
                                ? Icons.visibility_off
                                : Icons.visibility,
                            color: Colors.white),
                        onPressed: _toggleTooltips,
                        tooltip: showTooltips ? 'Hide Markers' : 'Show Markers',
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : _imagePath == null
              ? const Center(child: Text('No se pudo cargar la imagen'))
              : Stack(
                  children: [
                    LayoutBuilder(
                      builder: (context, constraints) {
                        debugPrint(
                            'Stack size: ${constraints.maxWidth} x ${constraints.maxHeight}');
                        final scaleX = constraints.maxWidth / imageWidth;
                        final scaleY = constraints.maxHeight / imageHeight;
                        final scale = scaleX < scaleY ? scaleX : scaleY;
                        final scaledWidth = imageWidth * scale;
                        final scaledHeight = imageHeight * scale;
                        debugPrint('Image size: $scaledWidth x $scaledHeight');
                        return Center(
                          child: Stack(
                            children: [
                              PhotoView.customChild(
                                controller: photoViewController,
                                backgroundDecoration: BoxDecoration(
                                  color: Colors.transparent,
                                ),
                                child: SizedBox(
                                  width: scaledWidth,
                                  height: scaledHeight,
                                  child: Stack(
                                    children: [
                                      Image.file(
                                        File(_imagePath!),
                                        width: scaledWidth,
                                        height: scaledHeight,
                                        fit: BoxFit.contain,
                                      ),
                                      ...markers.map((marker) {
                                        final scaledDx =
                                            marker.position.dx * scale;
                                        final scaledDy =
                                            marker.position.dy * scale;
                                        debugPrint(
                                            'Placing marker at: Offset($scaledDx, $scaledDy)');
                                        return Positioned(
                                          left: scaledDx,
                                          top: scaledDy,
                                          child: Stack(
                                            children: [
                                              Container(
                                                width:
                                                    marker.size * scale * 1.8,
                                                height:
                                                    marker.size * scale * 1.8,
                                                decoration: BoxDecoration(
                                                  color: marker.color,
                                                  shape: marker.shape ==
                                                          MarkerShape.circle
                                                      ? BoxShape.circle
                                                      : BoxShape.rectangle,
                                                  border: Border.all(
                                                      color: Colors.white,
                                                      width: 2),
                                                ),
                                                child: Center(
                                                  child: Text(
                                                    '${markers.indexOf(marker) + 1}',
                                                    style: TextStyle(
                                                      color: Colors.white,
                                                      fontSize: marker.size *
                                                          0.8 *
                                                          scale *
                                                          (photoViewController
                                                                  .scale ??
                                                              1.0),
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      }),
                                      if (showTooltips)
                                        ...markers.map((marker) {
                                          final scaledDx =
                                              marker.position.dx * scale;
                                          final scaledDy =
                                              marker.position.dy * scale;
                                          return Positioned(
                                            left: scaledDx,
                                            top: scaledDy - 30,
                                            child: Container(
                                              padding: EdgeInsets.all(4),
                                              margin:
                                                  EdgeInsets.only(bottom: 4),
                                              decoration: BoxDecoration(
                                                color: Colors.black,
                                                borderRadius:
                                                    BorderRadius.circular(4),
                                              ),
                                              child: Text(
                                                'Punto ${markers.indexOf(marker) + 1}: ${marker.description}',
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 12,
                                                ),
                                              ),
                                            ),
                                          );
                                        }),
                                    ],
                                  ),
                                ),
                              ),
                              Positioned(
                                bottom: 0,
                                left: 0,
                                right: 0,
                                child: GestureDetector(
                                  onTap: _toggleDescriptionDrawer,
                                  onVerticalDragUpdate: (details) {
                                    if (details.delta.dy < -5) {
                                      // Drag up - open drawer
                                      if (!showDescriptionDrawer) {
                                        setState(() {
                                          showDescriptionDrawer = true;
                                        });
                                      }
                                    } else if (details.delta.dy > 5) {
                                      // Drag down - close drawer
                                      if (showDescriptionDrawer) {
                                        setState(() {
                                          showDescriptionDrawer = false;
                                        });
                                      }
                                    }
                                  },
                                  child: Container(
                                    height: 40,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.vertical(
                                          top: Radius.circular(16)),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black26,
                                          blurRadius: 4,
                                          spreadRadius: 2,
                                        ),
                                      ],
                                    ),
                                    child: Center(
                                      child: Icon(
                                        showDescriptionDrawer
                                            ? Icons.keyboard_arrow_down
                                            : Icons.keyboard_arrow_up,
                                        size: 30,
                                        color: Colors.black54,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                    if (showDescriptionDrawer)
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Container(
                          height: 200,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black26,
                                blurRadius: 10,
                                spreadRadius: 5,
                              ),
                            ],
                          ),
                          child: SingleChildScrollView(
                            padding: EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Align(
                                  alignment: Alignment.topRight,
                                  child: IconButton(
                                    icon: Icon(Icons.close),
                                    onPressed: _toggleDescriptionDrawer,
                                  ),
                                ),
                                Text(
                                  imageDescription,
                                  style: TextStyle(
                                      fontSize: 16, color: Colors.black),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
    );
  }
}

class MarkerPoint {
  final Offset position;
  final String description;
  final double size;
  final Color color;
  final MarkerShape shape;

  MarkerPoint({
    required this.position,
    required this.description,
    required this.size,
    required this.color,
    required this.shape,
  });
}

enum MarkerShape { circle, square }
