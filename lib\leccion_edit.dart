// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:logger/logger.dart';
import 'package:intl/intl.dart';
import 'package:extended_text_field/extended_text_field.dart';
import 'package:flutter/gestures.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'galeria.dart';
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'imagenview.dart';
import 'imagen_edit.dart';
import 'leccionpage_componentes/bottomsheet.dart' as bs;
import 'cuadro_viewer.dart';
import 'cuadro_editor.dart';

class VisorDeImagen extends StatefulWidget {
  final String imagenPath;
  final TextEditingController? contenidoController;

  const VisorDeImagen(
      {super.key, required this.imagenPath, this.contenidoController});

  @override
  VisorDeImagenState createState() => VisorDeImagenState();
}

class VisorDeImagenState extends State<VisorDeImagen> {
  Future<List<String>> buscarImagenesRecursivamente(String rutaBase) async {
    final List<String> imagenes = [];
    final directory = Directory(rutaBase);

    if (await directory.exists()) {
      final archivos =
          directory.listSync(recursive: true); // Busca en subcarpetas
      for (var archivo in archivos) {
        if (archivo is File &&
            (archivo.path.endsWith('.jpg') || archivo.path.endsWith('.png'))) {
          imagenes.add(archivo.path);
        }
      }
    }
    return imagenes;
  }

  void insertarLeccionEnCursor(String leccionNombre) {
    final contenidoController = widget.contenidoController;
    if (contenidoController == null) return;

    final cursorPos = contenidoController.selection.baseOffset;
    final texto = contenidoController.text;
    final referencia = '[$leccionNombre]';

    final nuevoTexto = texto.replaceRange(
      cursorPos,
      cursorPos,
      referencia,
    );

    setState(() {
      contenidoController.text = nuevoTexto;
      final nuevaPosicion = cursorPos + referencia.length;
      contenidoController.selection =
          TextSelection.collapsed(offset: nuevaPosicion);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
          content:
              Text('Referencia a "$leccionNombre" insertada en el cursor')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Visor de Imagen'),
      ),
      body: Center(
        child: Image.file(
          File(widget.imagenPath),
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            return const Icon(Icons.broken_image, size: 100);
          },
        ),
      ),
    );
  }
}

class LeccionEditPage extends StatefulWidget {
  final String? leccionContenido;
  final String? leccionTitulo;
  final int? leccionId;
  final int? unidadesId;
  final int? materiaId;
  final int? categoriaId;
  final int? leccionId2;
  final String? createdAt;
  final String? creador;
  final String? editor;
  final String? seccion;
  final String? editedAt;

  const LeccionEditPage({
    super.key,
    this.leccionContenido,
    this.leccionTitulo,
    this.leccionId,
    this.unidadesId,
    this.materiaId,
    this.categoriaId,
    this.leccionId2,
    this.createdAt,
    this.seccion,
    this.editedAt,
    this.creador,
    this.editor,
  });

  @override
  LeccionEditPageState createState() => LeccionEditPageState();
}

class LeccionEditPageState extends State<LeccionEditPage> {
  final TextEditingController tituloController = TextEditingController();
  final TextEditingController contenidoController = TextEditingController();
  final TextEditingController seccionController = TextEditingController();
  final TextEditingController creadorController = TextEditingController();
  final TextEditingController editorController = TextEditingController();

  final TextEditingController leccionSearchController = TextEditingController();
  String leccionSearchQuery = '';
  Timer? _searchDebounceTimer;
  Timer? _searchDefinicionDebounceTimer;

  var logger = Logger();
  int? unidadSeleccionada;
  List<Map<String, dynamic>> unidades = [];
  int? materiaSeleccionada;
  List<Map<String, dynamic>> materias = [];
  int? categoriaSeleccionada;
  List<Map<String, dynamic>> categorias = [];
  int? categoria2Seleccionada;
  List<String> existingSections = [];
  List<Map<String, dynamic>> referenciasBanco = [];
  Map<String, String> diccionarioCache = {}; // Cache de definiciones

  String searchReferencia = '';
  String searchDefinicion = ''; // Variable para búsqueda de definiciones
  String _activePanel = ''; // Variable para controlar el panel activo

  bool isLoadingLecciones = false;
  String? leccionesError;

  final ValueNotifier<List<Map<String, dynamic>>> leccionesBusquedaNotifier =
      ValueNotifier<List<Map<String, dynamic>>>([]);
  final ValueNotifier<List<Map<String, dynamic>>> definicionesBusquedaNotifier =
      ValueNotifier<List<Map<String, dynamic>>>([]);

  // Variables para borrador simple
  Timer? _autoSaveTimer;
  final ValueNotifier<bool> _borradorGuardadoNotifier =
      ValueNotifier<bool>(true);

  @override
  void initState() {
    super.initState();

    // Inicializar datos básicos de forma síncrona
    _initializeSyncData();

    // Cargar datos asíncronos de forma secuencial
    _initializeAsyncData();

    // Cargar lista completa de lecciones al inicio
    _cargarTodasLasLecciones();

    // Cargar lista completa de definiciones al inicio
    _cargarTodasLasDefiniciones();
  }

  void _buscarLeccionesWithDebounce(String query) {
    _searchDebounceTimer?.cancel();
    _searchDebounceTimer = Timer(const Duration(milliseconds: 10), () {
      _buscarLecciones(query);
    });
  }

  void _buscarDefinicionesWithDebounce(String query) {
    _searchDefinicionDebounceTimer?.cancel();
    _searchDefinicionDebounceTimer =
        Timer(const Duration(milliseconds: 10), () {
      _buscarDefiniciones(query);
    });
  }

  Future<void> _cargarTodasLasDefiniciones() async {
    try {
      final response = await Supabase.instance.client
          .from('diccionario')
          .select()
          .order('definicion_nombre', ascending: true);

      definicionesBusquedaNotifier.value =
          List<Map<String, dynamic>>.from(response);
    } catch (e) {
      definicionesBusquedaNotifier.value = [];
    }
  }

  Future<void> _buscarDefiniciones(String query) async {
    searchDefinicion = query; // Actualizar la variable para el UI

    if (query.trim().isEmpty) {
      // Si el campo está vacío, mostrar todas las definiciones
      await _cargarTodasLasDefiniciones();
      return;
    }

    try {
      final response = await Supabase.instance.client
          .from('diccionario')
          .select()
          .or('definicion_nombre.ilike.%$query%,definicion_contenido.ilike.%$query%')
          .order('definicion_nombre', ascending: true);

      definicionesBusquedaNotifier.value =
          List<Map<String, dynamic>>.from(response);
    } catch (e) {
      definicionesBusquedaNotifier.value = [];
    }
  }

  Future<void> _cargarTodasLasLecciones() async {
    try {
      final response = await Supabase.instance.client
          .from('lecciones')
          .select()
          .order('leccion_nombre', ascending: true)
          .limit(100);

      leccionesBusquedaNotifier.value =
          List<Map<String, dynamic>>.from(response);
    } catch (e) {
      leccionesBusquedaNotifier.value = [];
    }
  }

  Future<void> _buscarLecciones(String query) async {
    if (query.trim().isEmpty) {
      // Si el campo está vacío, mostrar todas las lecciones
      await _cargarTodasLasLecciones();
      return;
    }

    try {
      final response = await Supabase.instance.client
          .from('lecciones')
          .select()
          .ilike('leccion_nombre', '%$query%')
          .order('leccion_nombre', ascending: true)
          .limit(50);

      leccionesBusquedaNotifier.value =
          List<Map<String, dynamic>>.from(response);
    } catch (e) {
      leccionesBusquedaNotifier.value = [];
    }
  }

  Future<void> _loadPrimaryHierarchy(int leccionId) async {
    try {
      // 1. Obtener unidades_id desde la tabla lecciones
      final leccionResponse = await Supabase.instance.client
          .from('lecciones')
          .select('unidades_id')
          .eq('leccion_id', leccionId)
          .single();

      final int? unidadesId = leccionResponse['unidades_id'];

      if (unidadesId == null) {
        // Manejar caso donde unidades_id es nulo
        return;
      }

      // 2. Obtener unidad y materia_id desde la tabla unidades
      final unidadResponse = await Supabase.instance.client
          .from('unidades')
          .select('unidades_id, unidad_nombre, materia_id')
          .eq('unidades_id', unidadesId)
          .single();

      final int? materiaId = unidadResponse['materia_id'];

      if (materiaId == null) {
        // Manejar caso donde materia_id es nulo
        return;
      }

      // 3. Obtener categoria_id desde la tabla materias
      final materiaResponse = await Supabase.instance.client
          .from('materias')
          .select('materia_id, materia_nombre, categoria_id')
          .eq('materia_id', materiaId)
          .single();

      final int? categoriaId = materiaResponse['categoria_id'];

      if (categoriaId == null) {
        // Manejar caso donde categoria_id es nulo
        return;
      }

      // 4. Cargar listas para dropdowns
      final categoriasResponse = await Supabase.instance.client
          .from('categoria')
          .select('categorias, categoria_nombre')
          .order('categoria_nombre', ascending: true);

      final materiasResponse = await Supabase.instance.client
          .from('materias')
          .select('materia_id, materia_nombre')
          .eq('categoria_id', categoriaId)
          .order('materia_nombre', ascending: true);

      final unidadesResponse = await Supabase.instance.client
          .from('unidades')
          .select('unidades_id, unidad_nombre')
          .eq('materia_id', materiaId)
          .order('unidad_nombre', ascending: true);

      setState(() {
        categorias = List<Map<String, dynamic>>.from(categoriasResponse);
        categoriaSeleccionada = categoriaId;

        materias = List<Map<String, dynamic>>.from(materiasResponse);
        materiaSeleccionada = materiaId;

        unidades = List<Map<String, dynamic>>.from(unidadesResponse);
        unidadSeleccionada = unidadesId;
      });
    } catch (e) {
      logger.e('Error cargando jerarquía principal: $e');
    }
  }

  Future<void> _loadSecondaryCategory(int? leccionId2) async {
    if (leccionId2 == null) {
      // Do not change categoria2Seleccionada if null, keep it as is
      return;
    }

    try {
      setState(() {
        categoria2Seleccionada = leccionId2;
      });
    } catch (e) {
      logger.e('Error cargando categoría secundaria: $e');
      setState(() {
        categoria2Seleccionada = null;
      });
    }
  }

  void _initializeSyncData() {
    loadExistingSections();

    // Cargar datos básicos primero (sin contenido)
    if (widget.leccionTitulo != null) {
      tituloController.text = widget.leccionTitulo!;
    }
    if (widget.seccion != null) {
      seccionController.text = widget.seccion!;
    }

    logger.d('📝 Parámetros recibidos:');
    logger.d('   - leccionId: ${widget.leccionId}');
    logger.d('   - unidadesId: ${widget.unidadesId}');
    logger.d('   - materiaId: ${widget.materiaId}');
    logger.d('   - categoriaId: ${widget.categoriaId}');
    logger.d('   - leccionId2: ${widget.leccionId2}');
  }

  Future<void> _initializeAsyncData() async {
    try {
      // 1. Cargar categorías primero
      await cargarCategorias();

      // 2. Cargar jerarquía principal basada en leccionId
      if (widget.leccionId != null) {
        await _loadPrimaryHierarchy(widget.leccionId!);
      } else {
        // Si es nueva lección, cargar todas las unidades
        logger.d('📝 Nueva lección - cargando todas las unidades');
        await cargarUnidades();
      }

      // 3. Cargar categoría secundaria independientemente
      await _loadSecondaryCategory(widget.leccionId2);

      // 4. Cargar otros datos
      if (widget.creador != null) {
        await cargarCreador();
      }
      if (widget.leccionId != null) {
        await cargarEditores();
        await cargarCreador();
      }

      // 5. Cargar datos adicionales
      _fetchReferenciasBanco();
      _cargarDiccionarioCache();

      // 6. Inicializar sistema de borrador ANTES de cargar contenido
      _inicializarBorrador();

      // 7. Agregar listeners para auto-guardado
      _agregarListenersAutoGuardado();
    } catch (e) {
      logger.e('Error inicializando datos asíncronos: $e');
    }
  }

  Future<void> _cargarDiccionarioCache() async {
    try {
      final response = await Supabase.instance.client
          .from('diccionario')
          .select('definicion_nombre, definicion_contenido');

      final Map<String, String> cache = {};
      for (final definicion in response) {
        final nombre = definicion['definicion_nombre'] as String?;
        final contenido = definicion['definicion_contenido'] as String?;
        if (nombre != null && contenido != null) {
          cache[nombre.toLowerCase()] = contenido;
        }
      }

      setState(() {
        diccionarioCache = cache;
      });
    } catch (e) {
      debugPrint('Error al cargar diccionario cache: $e');
    }
  }

  Widget _buildReferenciasPanel() {
    return DefaultTabController(
      length: 2,
      child: Column(
        children: [
          const DrawerHeader(child: Text('Banco de Referencias')),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Padding(
                padding: const EdgeInsets.only(right: 16.0, top: 8.0),
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.add),
                  label: const Text('Agregar referencia'),
                  onPressed: _mostrarDialogoAgregarReferencia,
                ),
              ),
            ],
          ),
          const TabBar(
            tabs: [
              Tab(text: 'En texto'),
              Tab(text: 'Repositorio'),
            ],
          ),
          Expanded(
            child: TabBarView(
              children: [
                _buildReferenciasEnTexto(),
                _buildRepositorioReferencias(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<String> extraerImagenesDelTexto(String texto) {
    final RegExp imagenExp = RegExp(r'!\[.*?\]\((.*?)\)');
    return imagenExp.allMatches(texto).map((m) => m.group(1)!).toList();
  }

  Future<void> loadExistingSections() async {
    final response = await Supabase.instance.client
        .from('lecciones')
        .select('seccion')
        .not('seccion', 'is', null);

    setState(() {
      existingSections = (response as List)
          .map((row) => row['seccion'] as String)
          .where((section) => section.isNotEmpty)
          .toSet()
          .toList();
    });
  }

  Future<List<String>> buscarImagenesRecursivamente(String rutaBase) async {
    final List<String> imagenes = [];
    final directory = Directory(rutaBase);

    if (await directory.exists()) {
      final archivos =
          directory.listSync(recursive: true); // Busca en subcarpetas
      for (var archivo in archivos) {
        if (archivo is File &&
            (archivo.path.endsWith('.jpg') || archivo.path.endsWith('.png'))) {
          imagenes.add(archivo.path);
        }
      }
    }
    return imagenes;
  }

  Widget _buildImagenesActivas() {
    // Extrae los nombres de las imágenes referenciadas en el texto
    List<String> extraerImagenesReferenciadas(String texto) {
      final RegExp imagenExp = RegExp(r'!\[.*?\]\((.*?)\)');
      return imagenExp.allMatches(texto).map((m) => m.group(1)!).toList();
    }

    return FutureBuilder<List<String>>(
      future: buscarImagenesRecursivamente('Galeria medicapp'), // Ruta base
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return const Center(child: Text('Error al cargar imágenes.'));
        }

        final List<String> imagenesEnDirectorio = snapshot.data ?? [];
        final List<String> imagenesReferenciadas =
            extraerImagenesReferenciadas(contenidoController.text);

        // Filtra las imágenes que están referenciadas en el texto y existen en el directorio
        final List<String> imagenesValidas =
            imagenesEnDirectorio.where((imagen) {
          final nombreImagen = imagen.split(Platform.pathSeparator).last;
          return imagenesReferenciadas.contains(nombreImagen);
        }).toList();

        if (imagenesValidas.isEmpty) {
          return const Center(
              child: Text('No se encontraron imágenes referenciadas.'));
        }

        return ListView.builder(
          itemCount: imagenesValidas.length,
          itemBuilder: (context, index) {
            final String imagen = imagenesValidas[index];
            final String nombreImagen =
                imagen.split(Platform.pathSeparator).last;

            return Card(
              child: ListTile(
                leading: Image.file(
                  File(imagen),
                  width: 50,
                  height: 50,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return const Icon(Icons.broken_image, size: 50);
                  },
                ),
                title: Text(nombreImagen),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.visibility, color: Colors.blue),
                      tooltip: 'Ver imagen',
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                VisorDeImagen(imagenPath: imagen),
                          ),
                        );
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.edit, color: Colors.orange),
                      tooltip: 'Editar imagen',
                      onPressed: () =>
                          _editarImagenExistente(imagen, nombreImagen),
                    ),
                    IconButton(
                      icon: const Icon(Icons.remove_circle, color: Colors.red),
                      tooltip: 'Quitar imagen del texto',
                      onPressed: () => _quitarImagenDelTexto(nombreImagen),
                    ),
                    IconButton(
                      icon: const Icon(Icons.add_circle, color: Colors.green),
                      tooltip: 'Insertar imagen en cursor',
                      onPressed: () => _insertarImagenEnCursor(nombreImagen),
                    ),
                  ],
                ),
                onTap: () {
                  // Abre el visor de imágenes al hacer clic
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => VisorDeImagen(imagenPath: imagen),
                    ),
                  );
                },
              ),
            );
          },
        );
      },
    );
  }

  Future<void> cargarUnidades() async {
    final response = await Supabase.instance.client
        .from('unidades')
        .select('unidades_id, unidad_nombre')
        .order('unidad_nombre', ascending: true);

    if (response.isNotEmpty) {
      setState(() {
        unidades = List<Map<String, dynamic>>.from(response);
        if (widget.unidadesId != null) {
          unidadSeleccionada = widget.unidadesId;
        }
      });
    }
  }

  Future<void> cargarCategorias() async {
    try {
      final response = await Supabase.instance.client
          .from('categoria')
          .select('categorias, categoria_nombre')
          .order('categoria_nombre', ascending: true);

      // Filtrar elementos válidos y eliminar duplicados
      final categoriasValidas = <Map<String, dynamic>>[];
      final idsVistos = <int>{};

      for (var categoria in response) {
        final categoriaId = categoria['categorias'];
        if (categoriaId != null && !idsVistos.contains(categoriaId)) {
          idsVistos.add(categoriaId);
          categoriasValidas.add(categoria);
        }
      }

      setState(() {
        categorias = categoriasValidas;
      });
    } catch (e) {
      logger.e('Error cargando categorías: $e');
      setState(() {
        categorias = [];
      });
    }
  }

  // Función auxiliar para cargar materias sin setState

  // Función auxiliar para cargar unidades sin setState

  Future<void> cargarMaterias(
      {int? categoriaId, bool resetSelections = true}) async {
    if (categoriaId == null) {
      setState(() {
        materias = [];
        materiaSeleccionada = null;
        unidades = [];
        unidadSeleccionada = null;
      });
      return;
    }

    try {
      final response = await Supabase.instance.client
          .from('materias')
          .select('materia_id, materia_nombre')
          .eq('categoria_id', categoriaId)
          .order('materia_nombre', ascending: true);

      // Filtrar elementos válidos y eliminar duplicados
      final materiasValidas = <Map<String, dynamic>>[];
      final idsVistos = <int>{};

      for (var materia in response) {
        final materiaId = materia['materia_id'];
        if (materiaId != null && !idsVistos.contains(materiaId)) {
          idsVistos.add(materiaId);
          materiasValidas.add(materia);
        }
      }

      setState(() {
        materias = materiasValidas;
        if (resetSelections) {
          materiaSeleccionada = null;
          unidades = [];
          unidadSeleccionada = null;
        }
      });
    } catch (e) {
      logger.e('Error cargando materias: $e');
      setState(() {
        materias = [];
        if (resetSelections) {
          materiaSeleccionada = null;
          unidades = [];
          unidadSeleccionada = null;
        }
      });
    }
  }

  Future<void> cargarUnidadesPorMateria(
      {int? materiaId, bool resetSelection = true}) async {
    if (materiaId == null) {
      setState(() {
        unidades = [];
        unidadSeleccionada = null;
      });
      return;
    }

    try {
      final response = await Supabase.instance.client
          .from('unidades')
          .select('unidades_id, unidad_nombre')
          .eq('materia_id', materiaId)
          .order('unidad_nombre', ascending: true);

      // Filtrar elementos válidos y eliminar duplicados
      final unidadesValidas = <Map<String, dynamic>>[];
      final idsVistos = <int>{};

      for (var unidad in response) {
        final unidadId = unidad['unidades_id'];
        if (unidadId != null && !idsVistos.contains(unidadId)) {
          idsVistos.add(unidadId);
          unidadesValidas.add(unidad);
        }
      }

      setState(() {
        unidades = unidadesValidas;
        if (resetSelection) {
          unidadSeleccionada = null;
        }
      });
    } catch (e) {
      logger.e('Error cargando unidades por materia: $e');
      setState(() {
        unidades = [];
        if (resetSelection) {
          unidadSeleccionada = null;
        }
      });
    }
  }

  // Función para cargar la jerarquía completa cuando se edita una lección existente

  Future<void> cargarCreador() async {
    // Solo cargar creador si estamos editando una lección existente
    if (widget.leccionId == null) {
      // Para nueva lección, mostrar el usuario actual como creador
      final user = Supabase.instance.client.auth.currentUser;
      setState(() {
        creadorController.text = user?.email ?? 'Usuario actual';
      });
      return;
    }

    try {
      final response = await Supabase.instance.client
          .from('leccion_creador')
          .select('creador_id, profiles!inner(full_name)')
          .eq('leccion_id', widget.leccionId as Object)
          .maybeSingle();

      if (response != null) {
        setState(() {
          creadorController.text = response['profiles']['full_name'];
        });
      }
    } catch (e) {
      logger.e('Error cargando creador: $e');
    }
  }

  Widget _buildCuadrosPanel() {
    return DefaultTabController(
      length: 2,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.teal[50],
            child: const Row(
              children: [
                Icon(Icons.table_chart, color: Colors.teal),
                SizedBox(width: 8),
                Text(
                  'Banco de Cuadros',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.teal,
                  ),
                ),
              ],
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Padding(
                padding: const EdgeInsets.only(right: 16.0, top: 8.0),
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.add),
                  label: const Text('Nuevo cuadro'),
                  onPressed: _mostrarDialogoNuevoCuadro,
                ),
              ),
            ],
          ),
          const TabBar(
            tabs: [
              Tab(text: 'Activos', icon: Icon(Icons.list)),
              Tab(text: 'Banco', icon: Icon(Icons.storage)),
            ],
          ),
          Expanded(
            child: TabBarView(
              children: [
                _buildCuadrosActivos(),
                _buildBancoCuadros(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCuadrosActivos() {
    // Extraer los IDs de cuadros referenciados en el texto
    List<String> extraerCuadrosReferenciados(String texto) {
      final RegExp cuadroExp = RegExp(r'\[CUADRO:(\d+)\]');
      return cuadroExp.allMatches(texto).map((m) => m.group(1)!).toList();
    }

    final List<String> cuadrosReferenciados =
        extraerCuadrosReferenciados(contenidoController.text);

    if (cuadrosReferenciados.isEmpty) {
      return const Center(
        child: Text('No hay cuadros insertados en el texto'),
      );
    }

    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _cargarCuadrosPorIds(cuadrosReferenciados),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        }

        final cuadros = snapshot.data ?? [];

        return ListView.builder(
          itemCount: cuadros.length,
          itemBuilder: (context, index) {
            final cuadro = cuadros[index];
            return Card(
              margin: const EdgeInsets.all(8.0),
              child: ListTile(
                leading: const Icon(Icons.table_chart, color: Colors.teal),
                title: Text(cuadro['titulo'] ?? 'Sin título'),
                subtitle: Text('ID: ${cuadro['cuadro_id']}'),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.visibility, color: Colors.blue),
                      tooltip: 'Ver cuadro',
                      onPressed: () => _verCuadro(cuadro),
                    ),
                    IconButton(
                      icon: const Icon(Icons.edit, color: Colors.orange),
                      tooltip: 'Editar cuadro',
                      onPressed: () => _editarCuadro(cuadro),
                    ),
                    IconButton(
                      icon: const Icon(Icons.remove_circle, color: Colors.red),
                      tooltip: 'Quitar del texto',
                      onPressed: () =>
                          _quitarCuadroDelTexto(cuadro['cuadro_id'].toString()),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildBancoCuadros() {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _cargarTodosLosCuadros(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        }

        final cuadros = snapshot.data ?? [];

        if (cuadros.isEmpty) {
          return const Center(
            child: Text('No hay cuadros disponibles'),
          );
        }

        return ListView.builder(
          itemCount: cuadros.length,
          itemBuilder: (context, index) {
            final cuadro = cuadros[index];
            final cuadroId = cuadro['cuadro_id'].toString();
            final yaInsertado =
                contenidoController.text.contains('[CUADRO:$cuadroId]');

            return Card(
              margin: const EdgeInsets.all(8.0),
              child: ListTile(
                leading: Icon(
                  Icons.table_chart,
                  color: yaInsertado ? Colors.green : Colors.teal,
                ),
                title: Text(cuadro['titulo'] ?? 'Sin título'),
                subtitle: Text('ID: $cuadroId'),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.visibility, color: Colors.blue),
                      tooltip: 'Ver cuadro',
                      onPressed: () => _verCuadro(cuadro),
                    ),
                    IconButton(
                      icon: const Icon(Icons.edit, color: Colors.orange),
                      tooltip: 'Editar cuadro',
                      onPressed: () => _editarCuadro(cuadro),
                    ),
                    IconButton(
                      icon: Icon(
                        yaInsertado ? Icons.check_circle : Icons.add_circle,
                        color: yaInsertado ? Colors.green : Colors.blue,
                      ),
                      tooltip:
                          yaInsertado ? 'Ya insertado' : 'Insertar en cursor',
                      onPressed: yaInsertado
                          ? null
                          : () => _insertarCuadroEnCursor(cuadroId),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Future<List<Map<String, dynamic>>> _cargarTodosLosCuadros() async {
    try {
      final response = await Supabase.instance.client
          .from('cuadros')
          .select('cuadro_id, titulo, created_at')
          .order('titulo', ascending: true);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Error cargando cuadros: $e');
      return [];
    }
  }

  Future<List<Map<String, dynamic>>> _cargarCuadrosPorIds(
      List<String> ids) async {
    try {
      final response = await Supabase.instance.client
          .from('cuadros')
          .select('cuadro_id, titulo, created_at')
          .inFilter('cuadro_id', ids.map((id) => int.parse(id)).toList())
          .order('titulo', ascending: true);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Error cargando cuadros por IDs: $e');
      return [];
    }
  }

  void _insertarCuadroEnCursor(String cuadroId) async {
    final cursorPos = contenidoController.selection.baseOffset;
    final texto = contenidoController.text;

    // Insertar el cuadro en la posición del cursor
    final nuevoTexto = texto.replaceRange(
      cursorPos,
      cursorPos,
      '[CUADRO:$cuadroId]',
    );

    setState(() {
      contenidoController.text = nuevoTexto;
      // Mover el cursor después del cuadro insertado
      final nuevaPosicion = cursorPos + '[CUADRO:$cuadroId]'.length;
      contenidoController.selection =
          TextSelection.collapsed(offset: nuevaPosicion);
    });

    // Crear la relación en leccion_cuadros
    await _crearRelacionLeccionCuadro(cuadroId);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Cuadro $cuadroId insertado en el cursor')),
    );
  }

  void _quitarCuadroDelTexto(String cuadroId) async {
    final texto = contenidoController.text;
    final cuadroRef = '[CUADRO:$cuadroId]';

    if (!texto.contains(cuadroRef)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('El cuadro $cuadroId no está en el texto')),
      );
      return;
    }

    final nuevoTexto = texto.replaceAll(cuadroRef, '');

    setState(() {
      contenidoController.text = nuevoTexto;
    });

    // Eliminar la relación de leccion_cuadros
    await _eliminarRelacionLeccionCuadro(cuadroId);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Cuadro $cuadroId removido del texto')),
    );
  }

  void _verCuadro(Map<String, dynamic> cuadro) {
    // Por ahora, mostrar un diálogo simple con la información
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(cuadro['titulo'] ?? 'Sin título'),
        content:
            Text('ID: ${cuadro['cuadro_id']}\nCreado: ${cuadro['created_at']}'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cerrar'),
          ),
        ],
      ),
    );
  }

  void _editarCuadro(Map<String, dynamic> cuadro) async {
    final cuadroId = await Navigator.push<String>(
      context,
      MaterialPageRoute(
        builder: (context) => CuadroEditor(
          cuadroId: cuadro['cuadro_id'] as int?,
          leccionId: widget.leccionId,
        ),
      ),
    );

    if (cuadroId != null) {
      // Refrescar el panel
      setState(() {});

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Cuadro actualizado')),
      );
    }
  }

  void _mostrarDialogoNuevoCuadro() async {
    final cuadroId = await Navigator.push<String>(
      context,
      MaterialPageRoute(
        builder: (context) => CuadroEditor(leccionId: widget.leccionId),
      ),
    );

    if (cuadroId != null) {
      // Insertar el cuadro en el cursor
      _insertarCuadroEnCursor(cuadroId);

      // Refrescar el panel
      setState(() {});
    }
  }

  Future<void> _crearRelacionLeccionCuadro(String cuadroId) async {
    // Solo crear relación si estamos editando una lección existente
    if (widget.leccionId == null) {
      debugPrint(
          '⚠️ No se puede crear relación leccion_cuadros: leccionId es null');
      return;
    }

    try {
      // Verificar si la relación ya existe
      final existeRelacion = await Supabase.instance.client
          .from('leccion_cuadros')
          .select('id')
          .eq('leccion_id', widget.leccionId!)
          .eq('cuadro_id', int.parse(cuadroId))
          .maybeSingle();

      if (existeRelacion == null) {
        // Crear la relación si no existe
        await Supabase.instance.client.from('leccion_cuadros').insert({
          'leccion_id': widget.leccionId!,
          'cuadro_id': int.parse(cuadroId),
        });
        debugPrint(
            '✅ Relación leccion_cuadros creada: leccion=${widget.leccionId}, cuadro=$cuadroId');
      } else {
        debugPrint(
            'ℹ️ Relación leccion_cuadros ya existe: leccion=${widget.leccionId}, cuadro=$cuadroId');
      }
    } catch (e) {
      debugPrint('❌ Error creando relación leccion_cuadros: $e');
    }
  }

  Future<void> _eliminarRelacionLeccionCuadro(String cuadroId) async {
    // Solo eliminar relación si estamos editando una lección existente
    if (widget.leccionId == null) {
      debugPrint(
          '⚠️ No se puede eliminar relación leccion_cuadros: leccionId es null');
      return;
    }

    try {
      await Supabase.instance.client
          .from('leccion_cuadros')
          .delete()
          .eq('leccion_id', widget.leccionId!)
          .eq('cuadro_id', int.parse(cuadroId));

      debugPrint(
          '✅ Relación leccion_cuadros eliminada: leccion=${widget.leccionId}, cuadro=$cuadroId');
    } catch (e) {
      debugPrint('❌ Error eliminando relación leccion_cuadros: $e');
    }
  }

  Future<void> cargarEditores() async {
    // Solo cargar editores si estamos editando una lección existente
    if (widget.leccionId == null) {
      // Para nueva lección, mostrar el usuario actual como creador
      final user = Supabase.instance.client.auth.currentUser;
      setState(() {
        editorController.text = user?.email ?? 'Usuario actual';
      });
      return;
    }

    final response = await Supabase.instance.client
        .from('leccion_editors')
        .select('editor_id, profiles!inner(full_name)')
        .eq('leccion_id', widget.leccionId as Object);

    setState(() {
      // This will join all editor names with a comma and space
      editorController.text = (response as List)
          .map((row) => row['profiles']['full_name'] as String)
          .toList()
          .join(', ');
    });
  }

  Widget _buildDefinicionesPanel() {
    return DefaultTabController(
      length: 3,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16.0),
            color: Colors.blue[50],
            child: const Row(
              children: [
                Icon(Icons.book, color: Colors.blue),
                SizedBox(width: 8),
                Text(
                  'Banco de Definiciones',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
          ),
          const TabBar(
            tabs: [
              Tab(text: 'Activas', icon: Icon(Icons.list)),
              Tab(text: 'Banco', icon: Icon(Icons.library_books)),
              Tab(text: 'Nueva', icon: Icon(Icons.add)),
            ],
          ),
          Expanded(
            child: TabBarView(
              children: [
                _buildDefinicionesActivas(),
                _buildListaDefiniciones(),
                _buildNuevaDefinicionForm(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListaDefiniciones() {
    return Column(
      children: [
        // Campo de búsqueda
        Container(
          padding: const EdgeInsets.all(12.0),
          color: Colors.blue[50],
          child: TextField(
            onChanged: (value) {
              _buscarDefinicionesWithDebounce(value);
            },
            decoration: InputDecoration(
              hintText: 'Buscar definición...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: searchDefinicion.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _buscarDefinicionesWithDebounce('');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
            ),
          ),
        ),
        // Lista de definiciones filtrada
        Expanded(
          child: ValueListenableBuilder<List<Map<String, dynamic>>>(
            valueListenable: definicionesBusquedaNotifier,
            builder: (context, definiciones, child) {
              if (definiciones.isEmpty && searchDefinicion.isNotEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.search_off,
                          size: 64, color: Colors.grey),
                      const SizedBox(height: 16),
                      Text(
                        'No se encontraron definiciones para "$searchDefinicion"',
                        style:
                            const TextStyle(fontSize: 16, color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                );
              }

              if (definiciones.isEmpty) {
                return const Center(
                    child: Text('No hay definiciones disponibles.'));
              }

              return ListView.builder(
                itemCount: definiciones.length,
                itemBuilder: (context, index) {
                  final definicion = definiciones[index];
                  final nombre = definicion['definicion_nombre'] ?? '';
                  final contenido = definicion['definicion_contenido'] ?? '';

                  return Card(
                    margin: const EdgeInsets.symmetric(
                        horizontal: 8.0, vertical: 4.0),
                    child: ListTile(
                      leading:
                          const Icon(Icons.library_books, color: Colors.blue),
                      title: Text(
                        nombre,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      subtitle: Text(
                        contenido,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      trailing: IconButton(
                        icon: const Icon(Icons.input, color: Colors.green),
                        tooltip: 'Insertar en texto',
                        onPressed: () {
                          final cursorPos =
                              contenidoController.selection.baseOffset;
                          final text = contenidoController.text;
                          final linkDefinicion =
                              '[$nombre](#${nombre.toLowerCase().replaceAll(' ', '_')})';
                          final newText = text.replaceRange(
                            cursorPos,
                            cursorPos,
                            linkDefinicion,
                          );
                          setState(() {
                            contenidoController.text = newText;
                            contenidoController.selection =
                                TextSelection.collapsed(
                              offset: cursorPos + linkDefinicion.length,
                            );
                          });

                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                                content:
                                    Text('Definición "$nombre" insertada')),
                          );
                        },
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildNuevaDefinicionForm() {
    final nombreController = TextEditingController();
    final contenidoController = TextEditingController();

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          TextField(
            controller: nombreController,
            decoration: const InputDecoration(
              labelText: 'Nombre de la Definición',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 200,
            child: ExtendedTextField(
              controller: contenidoController,
              maxLines: null,
              specialTextSpanBuilder: ReferenciaSpanBuilder(
                leccionId: -1,
                context: context,
                diccionarioCache: {},
                textController: contenidoController,
              ),
              decoration: const InputDecoration(
                labelText: 'Contenido de la Definición',
                border: OutlineInputBorder(),
              ),
            ),
          ),
          const Spacer(),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () async {
                await Supabase.instance.client.from('diccionario').insert({
                  'definicion_nombre': nombreController.text,
                  'definicion_contenido': contenidoController.text,
                  'created_at': DateTime.now().toIso8601String(),
                });
                nombreController.clear();
                contenidoController.clear();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                      content: Text('Definición agregada exitosamente')),
                );
              },
              child: const Text('Guardar Definición'),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _abrirGaleria() async {
    final urlImagenSeleccionada = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Galeria(
          leccionId: widget.leccionId ?? -1, // -1 para nueva lección
        ),
      ),
    );

    if (urlImagenSeleccionada != null && mounted) {
      final nombreImagenSeleccionada =
          urlImagenSeleccionada.split(RegExp(r'[\\/]+')).last;
      final selectedText =
          contenidoController.selection.textInside(contenidoController.text);
      final newText = contenidoController.text.replaceRange(
        contenidoController.selection.start,
        contenidoController.selection.end,
        '![$selectedText]($nombreImagenSeleccionada)',
      );
      setState(() {
        contenidoController.text = newText;
        final newOffset = contenidoController.selection.start + newText.length;
        contenidoController.selection = TextSelection.collapsed(
          offset: newOffset,
        );
      });
    }
  }

  Future<void> _editarImagenExistente(
      String imagePath, String nombreImagen) async {
    try {
      // Buscar el imagen_id en la tabla imagenes
      final imageResponse = await Supabase.instance.client
          .from('imagenes')
          .select('imagen_id')
          .eq('imagen_nombre', nombreImagen)
          .maybeSingle();

      if (imageResponse == null) {
        throw Exception('Imagen no encontrada en la base de datos');
      }

      final imageId = imageResponse['imagen_id'];

      // Buscar los datos existentes en leccion_imagenes
      final leccionImagenResponse = await Supabase.instance.client
          .from('leccion_imagenes')
          .select('descripcion_especifica, markers_data')
          .eq('imagen_id', imageId)
          .eq('leccion_id', widget.leccionId ?? -1)
          .maybeSingle();

      // Navegar a ImagenEdit con los datos existentes (si los hay)
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ImagenEdit(
            imagePath: imagePath,
            leccionId: widget.leccionId ?? -1,
            imageId: imageId,
            existingData: leccionImagenResponse, // Puede ser null
          ),
        ),
      );

      // Si se guardó exitosamente, actualizar la vista
      if (result != null && mounted) {
        setState(() {
          // Refrescar el panel de imágenes
          // Dummy usage of result to avoid unused variable warning
          var _ = result;
        });
      }
    } catch (e) {
      debugPrint('Error al cargar datos de imagen existente: $e');
      // Si no hay datos existentes, abrir ImagenEdit normal
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ImagenEdit(
            imagePath: imagePath,
            leccionId: widget.leccionId ?? -1,
          ),
        ),
      );
    }
  }

  void _quitarImagenDelTexto(String nombreImagen) {
    final texto = contenidoController.text;

    // Buscar todas las referencias a esta imagen en el texto
    final RegExp imagenExp =
        RegExp(r'!\[([^\]]*)\]\(' + RegExp.escape(nombreImagen) + r'\)');
    final matches = imagenExp.allMatches(texto).toList();

    if (matches.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(
                'No se encontraron referencias a "$nombreImagen" en el texto')),
      );
      return;
    }

    // Mostrar diálogo de confirmación
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Quitar imagen del texto'),
        content: Text(
            'Se encontraron ${matches.length} referencia(s) a "$nombreImagen" en el texto.\n\n'
            '¿Deseas eliminar todas las referencias?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () async {
              // Eliminar todas las referencias a la imagen del texto
              final nuevoTexto = texto.replaceAll(imagenExp, '');
              setState(() {
                contenidoController.text = nuevoTexto;
              });
              Navigator.pop(context);

              // Eliminar también de la base de datos
              await _eliminarImagenDeBaseDatos(nombreImagen);

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                    content: Text(
                        'Se eliminaron ${matches.length} referencia(s) a "$nombreImagen" del texto y de la base de datos')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Eliminar'),
          ),
        ],
      ),
    );
  }

  Future<void> _eliminarImagenDeBaseDatos(String nombreImagen) async {
    try {
      // Buscar el imagen_id en la tabla imagenes
      final imageResponse = await Supabase.instance.client
          .from('imagenes')
          .select('imagen_id')
          .eq('imagen_nombre', nombreImagen)
          .maybeSingle();

      if (imageResponse == null) {
        debugPrint(
            '⚠️ No se encontró la imagen "$nombreImagen" en la base de datos');
        return;
      }

      final imageId = imageResponse['imagen_id'];
      debugPrint(
          '🗑️ Eliminando imagen de la base de datos - ID: $imageId, Nombre: $nombreImagen');

      // Eliminar de leccion_imagenes (relación con esta lección específica)
      await Supabase.instance.client
          .from('leccion_imagenes')
          .delete()
          .eq('imagen_id', imageId)
          .eq('leccion_id', widget.leccionId ?? -1);
      debugPrint('✅ Eliminado de leccion_imagenes');

      // Verificar si la imagen está siendo usada en otras lecciones
      final otrasLecciones = await Supabase.instance.client
          .from('leccion_imagenes')
          .select('leccion_id')
          .eq('imagen_id', imageId);

      if (otrasLecciones.isEmpty) {
        // Si no está siendo usada en otras lecciones, eliminar completamente
        debugPrint(
            '🗑️ Imagen no usada en otras lecciones, eliminando completamente');

        // Eliminar de imagen_creador
        await Supabase.instance.client
            .from('imagen_creador')
            .delete()
            .eq('imagen_id', imageId);
        debugPrint('✅ Eliminado de imagen_creador');

        // Eliminar de imagenes
        await Supabase.instance.client
            .from('imagenes')
            .delete()
            .eq('imagen_id', imageId);
        debugPrint('✅ Eliminado de imagenes');
      } else {
        debugPrint(
            'ℹ️ Imagen conservada - está siendo usada en ${otrasLecciones.length} otra(s) lección(es)');
      }
    } catch (e) {
      debugPrint('❌ Error al eliminar imagen de la base de datos: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text('Error al eliminar imagen de la base de datos: $e')),
      );
    }
  }

  void _insertarImagenEnCursor(String nombreImagen) {
    final cursorPos = contenidoController.selection.baseOffset;
    final texto = contenidoController.text;

    // Insertar la imagen en la posición del cursor
    final nuevoTexto = texto.replaceRange(
      cursorPos,
      cursorPos,
      '![]($nombreImagen)',
    );

    setState(() {
      contenidoController.text = nuevoTexto;
      // Mover el cursor después de la imagen insertada
      final nuevaPosicion = cursorPos + '![]($nombreImagen)'.length;
      contenidoController.selection =
          TextSelection.collapsed(offset: nuevaPosicion);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Imagen "$nombreImagen" insertada en el cursor')),
    );
  }

  Future<void> actualizarLeccion() async {
    final user = Supabase.instance.client.auth.currentUser;
    logger.d('Current user: $user');

    if (user == null) {
      logger.w('No user logged in!');
      return;
    }

    final now = DateTime.now();
    final formattedDate = DateFormat('yyyy-MM-dd').format(now);

    try {
      // First check if this editor already exists
      final existingEditor = await Supabase.instance.client
          .from('leccion_editors')
          .select()
          .eq('leccion_id', widget.leccionId as Object)
          .eq('editor_id', user.id)
          .maybeSingle();

      // Update main lesson content excluding secondary category
      await Supabase.instance.client
          .from('lecciones')
          .update({
            'unidades_id': unidadSeleccionada,
            'leccion_nombre': tituloController.text,
            'leccion_contenido': contenidoController.text,
            'seccion': seccionController.text,
            'edited_at': formattedDate,
          })
          .eq('leccion_id', widget.leccionId as Object)
          .select();

      // Update secondary category independently if not null
      if (categoria2Seleccionada != null) {
        await Supabase.instance.client
            .from('lecciones')
            .update({'leccion_id2': categoria2Seleccionada})
            .eq('leccion_id', widget.leccionId as Object)
            .select();
      }

      logger.d('Lesson update successful');

      // Only insert if editor doesn't exist
      if (existingEditor == null) {
        await Supabase.instance.client.from('leccion_editors').insert({
          'leccion_id': widget.leccionId,
          'editor_id': user.id,
        }).select();
      }

      // Eliminar borrador después de guardar exitosamente
      await _eliminarBorradorDespuesDeGuardar();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Lección actualizada correctamente')));
        Navigator.pop(context);
      }
    } catch (e) {
      logger.e('Error updating lesson: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error al actualizar la lección: $e')));
      }
    }
  }

  Future<void> crearLeccion() async {
    final user = Supabase.instance.client.auth.currentUser;
    logger.d('Current user: $user');

    if (user == null) {
      logger.w('No user logged in!');
      return;
    }

    // Validar campos requeridos
    if (tituloController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('El título es obligatorio')),
      );
      return;
    }

    if (unidadSeleccionada == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Debe seleccionar una unidad')),
      );
      return;
    }

    final now = DateTime.now();
    final formattedDate = DateFormat('yyyy-MM-dd').format(now);

    try {
      // Crear nueva lección sin secondary category
      final leccionResponse = await Supabase.instance.client
          .from('lecciones')
          .insert({
            'unidades_id': unidadSeleccionada,
            'leccion_nombre': tituloController.text.trim(),
            'leccion_contenido': contenidoController.text,
            'seccion': seccionController.text.trim(),
            'created_at': formattedDate,
          })
          .select()
          .single();

      final nuevaLeccionId = leccionResponse['leccion_id'];
      logger.d('Nueva lección creada con ID: $nuevaLeccionId');

      // Registrar secondary category independently if not null
      if (categoria2Seleccionada != null) {
        await Supabase.instance.client
            .from('lecciones')
            .update({'leccion_id2': categoria2Seleccionada})
            .eq('leccion_id', nuevaLeccionId)
            .select();
      }

      // Registrar creador
      await Supabase.instance.client.from('leccion_creador').insert({
        'leccion_id': nuevaLeccionId,
        'creador_id': user.id,
      });

      logger.d('Lección y creador registrados');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Lección creada correctamente')),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      logger.e('Error creating lesson: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error al crear la lección: $e')),
        );
      }
    }
  }

  Future<void> _fetchReferenciasBanco() async {
    final data = await Supabase.instance.client
        .from('referencias')
        .select()
        .order('created_at');
    setState(() {
      referenciasBanco = List<Map<String, dynamic>>.from(data);
    });
  }

  void _insertarReferenciaEnTexto(Map<String, dynamic> referencia) {
    final cursorPos = contenidoController.selection.baseOffset;
    final text = contenidoController.text;
    final newText =
        text.replaceRange(cursorPos, cursorPos, '[ref:${referencia['id']}]');
    setState(() {
      contenidoController.text = newText;
      contenidoController.selection = TextSelection.collapsed(
          offset: cursorPos + '[ref:${referencia['id']}]'.length);
    });
  }

  String formatoVancouver(Map<String, dynamic> ref) {
    // Puedes ajustar el formato según tus necesidades
    String autores = ref['autores'] ?? '';
    String titulo = ref['titulo'] ?? '';
    String revista = ref['revista'] ?? '';
    String year = ref['year'] ?? '';
    String volumen = ref['volumen'] ?? '';
    String paginas = ref['paginas'] ?? '';
    String doi = ref['doi'] ?? '';
    String url = ref['url'] ?? '';

    String base = '$autores. $titulo. $revista. $year;$volumen:$paginas.';
    if (doi.isNotEmpty) base += ' doi:$doi.';
    if (url.isNotEmpty) base += ' $url';
    return base;
  }

  String procesarReferenciasPreview(
      String texto, List<Map<String, dynamic>> referenciasBanco) {
    final refExp = RegExp(r'\[ref:(\d+)\]');
    final idsEnOrden = <String>[];
    final refIdToNumero = <String, int>{};

    // Encuentra todos los IDs en orden de aparición
    for (final match in refExp.allMatches(texto)) {
      final id = match.group(1)!;
      if (!idsEnOrden.contains(id)) {
        idsEnOrden.add(id);
      }
    }
    for (var i = 0; i < idsEnOrden.length; i++) {
      refIdToNumero[idsEnOrden[i]] = i + 1;
    }

    // Reemplaza en el texto
    String textoProcesado = texto.replaceAllMapped(refExp, (match) {
      final id = match.group(1)!;
      final numero = refIdToNumero[id] ?? '?';
      return '[$numero]';
    });

    // Agrega la lista de referencias al final en formato Vancouver
    if (idsEnOrden.isNotEmpty) {
      textoProcesado += '\n\n**Referencias:**\n';
      for (var i = 0; i < idsEnOrden.length; i++) {
        final ref = referenciasBanco.firstWhere(
          (r) => r['id'].toString() == idsEnOrden[i],
          orElse: () => <String, dynamic>{},
        );
        textoProcesado += '[${i + 1}] ${formatoVancouver(ref)}\n';
      }
    }
    return textoProcesado;
  }

  // Ejemplo de formulario simple para agregar referencia
  Future<void> _mostrarDialogoAgregarReferencia() async {
    final autoresController = TextEditingController();
    final tituloController = TextEditingController();
    final revistaController = TextEditingController();
    final yearController = TextEditingController();
    final volumenController = TextEditingController();
    final paginasController = TextEditingController();
    final doiController = TextEditingController();
    final urlController = TextEditingController();

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Agregar nueva referencia'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                  controller: autoresController,
                  decoration: const InputDecoration(labelText: 'Autores')),
              TextField(
                  controller: tituloController,
                  decoration: const InputDecoration(labelText: 'Título')),
              TextField(
                  controller: revistaController,
                  decoration: const InputDecoration(labelText: 'Revista')),
              TextField(
                  controller: yearController,
                  decoration: const InputDecoration(labelText: 'Año')),
              TextField(
                  controller: volumenController,
                  decoration: const InputDecoration(labelText: 'Volumen')),
              TextField(
                  controller: paginasController,
                  decoration: const InputDecoration(labelText: 'Páginas')),
              TextField(
                  controller: doiController,
                  decoration: const InputDecoration(labelText: 'DOI')),
              TextField(
                  controller: urlController,
                  decoration: const InputDecoration(labelText: 'URL')),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () async {
              await Supabase.instance.client.from('referencias').insert({
                'autores': autoresController.text,
                'titulo': tituloController.text,
                'revista': revistaController.text,
                'year': yearController.text,
                'volumen': volumenController.text,
                'paginas': paginasController.text,
                'doi': doiController.text,
                'url': urlController.text,
              });
              Navigator.pop(context); // Usa el context del builder del diálogo
              _fetchReferenciasBanco();
            },
            child: const Text('Guardar'),
          ),
        ],
      ),
    );
  }

  Future<void> _borrarReferencia(int id) async {
    final response = await Supabase.instance.client
        .from('referencias')
        .update({'activa': false})
        .eq('id', id)
        .select(); // <-- Esto te devuelve el registro actualizado
    // ignore: avoid_print
    print('Update response: $response');
    // Limpia el texto si quieres
    final refTag = '[ref:$id]';
    final oldText = contenidoController.text;
    final newText = oldText.replaceAll(refTag, '');
    if (oldText != newText) {
      setState(() {
        contenidoController.text = newText;
      });
    }
    _fetchReferenciasBanco();
  }

  Widget _buildReferenciasEnTexto() {
    final idsUsados = idsReferenciasEnTexto(contenidoController.text);
    final usadas =
        referenciasBanco.where((ref) => idsUsados.contains(ref['id'])).toList();

    if (usadas.isEmpty) {
      return const Center(child: Text('No hay referencias en el texto.'));
    }

    return ListView.builder(
      itemCount: usadas.length,
      itemBuilder: (context, index) {
        final ref = usadas[index];
        return Card(
          color: Colors.lightGreen[100],
          child: ListTile(
            title: Text(
              'ID: ${ref['id']} - ${ref['titulo'] ?? '(Sin título)'}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(formatoVancouver(ref)),
            trailing: IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              tooltip: 'Quitar referencia del texto',
              onPressed: () async {
                final confirm = await showDialog<bool>(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Confirmar borrado'),
                    content: const Text(
                        '¿Seguro que deseas quitar esta referencia del texto?'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context, false),
                        child: const Text('Cancelar'),
                      ),
                      ElevatedButton(
                        onPressed: () => Navigator.pop(context, true),
                        child: const Text('Borrar'),
                      ),
                    ],
                  ),
                );
                if (confirm == true) {
                  _borrarReferencia(ref['id']);
                }
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildRepositorioReferencias() {
    final referenciasFiltradas = searchReferencia.isEmpty
        ? referenciasBanco
        : referenciasBanco
            .where((ref) =>
                (ref['titulo'] ?? '')
                    .toString()
                    .toLowerCase()
                    .contains(searchReferencia) ||
                (ref['autores'] ?? '')
                    .toString()
                    .toLowerCase()
                    .contains(searchReferencia) ||
                (ref['revista'] ?? '')
                    .toString()
                    .toLowerCase()
                    .contains(searchReferencia) ||
                (ref['year'] ?? '')
                    .toString()
                    .toLowerCase()
                    .contains(searchReferencia) ||
                (ref['volumen'] ?? '')
                    .toString()
                    .toLowerCase()
                    .contains(searchReferencia) ||
                (ref['paginas'] ?? '')
                    .toString()
                    .toLowerCase()
                    .contains(searchReferencia) ||
                (ref['doi'] ?? '')
                    .toString()
                    .toLowerCase()
                    .contains(searchReferencia) ||
                (ref['url'] ?? '')
                    .toString()
                    .toLowerCase()
                    .contains(searchReferencia))
            .toList();

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: TextField(
            decoration: const InputDecoration(
              labelText: 'Buscar referencia',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              searchReferencia = value.toLowerCase();
              (this as dynamic).setState(() {});
            },
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: referenciasFiltradas.length,
            itemBuilder: (context, index) {
              final ref = referenciasFiltradas[index];
              final isActiva = ref['activa'] == true;
              return Card(
                color: isActiva ? Colors.grey[100] : Colors.grey[300],
                child: ListTile(
                  title: Text(
                    'ID: ${ref['id']} - ${ref['titulo'] ?? '(Sin título)'}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: isActiva ? Colors.black : Colors.grey[600],
                    ),
                  ),
                  subtitle: Text(formatoVancouver(ref)),
                  trailing: IconButton(
                    icon: const Icon(Icons.input),
                    tooltip: 'Insertar en texto',
                    onPressed:
                        isActiva ? () => _insertarReferenciaEnTexto(ref) : null,
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Set<int> idsReferenciasEnTexto(String texto) {
    final refExp = RegExp(r'\[ref:(\d+)\]');
    return refExp.allMatches(texto).map((m) => int.parse(m.group(1)!)).toSet();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title:
            Text(widget.leccionId == null ? 'Nueva Lección' : 'Editar Lección'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
        bottom: PreferredSize(
          preferredSize:
              const Size.fromHeight(96.0), // Aumentamos la altura para 2 filas
          child: Column(
            children: [
              // Segunda fila: Iconos de paneles
              Container(
                height: 48.0,
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  children: [
                    IconButton(
                      icon: Icon(
                        Icons.menu,
                        color: _activePanel == 'referencias'
                            ? Colors.blue
                            : Colors.grey[600],
                      ),
                      tooltip: 'Banco de Referencias',
                      onPressed: () {
                        setState(() {
                          _activePanel = _activePanel == 'referencias'
                              ? ''
                              : 'referencias';
                        });
                      },
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.image,
                        color: _activePanel == 'imagenes'
                            ? Colors.green
                            : Colors.grey[600],
                      ),
                      tooltip: 'Banco de Imágenes',
                      onPressed: () {
                        setState(() {
                          _activePanel =
                              _activePanel == 'imagenes' ? '' : 'imagenes';
                        });
                      },
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.book,
                        color: _activePanel == 'definiciones'
                            ? Colors.orange
                            : Colors.grey[600],
                      ),
                      tooltip: 'Banco de Definiciones',
                      onPressed: () {
                        setState(() {
                          _activePanel = _activePanel == 'definiciones'
                              ? ''
                              : 'definiciones';
                        });
                      },
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.table_chart,
                        color: _activePanel == 'cuadros'
                            ? Colors.teal
                            : Colors.grey[600],
                      ),
                      tooltip: 'Banco de Cuadros',
                      onPressed: () {
                        setState(() {
                          _activePanel =
                              _activePanel == 'cuadros' ? '' : 'cuadros';
                        });
                      },
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.visibility,
                        color: _activePanel == 'markdown'
                            ? Colors.purple
                            : Colors.grey[600],
                      ),
                      tooltip: 'Vista Previa Markdown',
                      onPressed: () {
                        setState(() {
                          _activePanel =
                              _activePanel == 'markdown' ? '' : 'markdown';
                        });
                      },
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.menu_book,
                        color: _activePanel == 'lecciones'
                            ? Colors.deepPurple
                            : Colors.grey[600],
                      ),
                      tooltip: 'Banco de Lecciones',
                      onPressed: () {
                        setState(() {
                          _activePanel =
                              _activePanel == 'lecciones' ? '' : 'lecciones';
                        });
                      },
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.settings,
                        color: _activePanel == 'admin'
                            ? Colors.blue
                            : Colors.grey[600],
                      ),
                      tooltip: 'Administración',
                      onPressed: () {
                        setState(() {
                          _activePanel = _activePanel == 'admin' ? '' : 'admin';
                        });
                      },
                    ),
                    // Separador visual
                    Container(
                      height: 24,
                      width: 1,
                      color: Colors.grey[400],
                      margin: EdgeInsets.symmetric(horizontal: 8),
                    ),
                    // Botón Guardar a Lección
                    IconButton(
                      icon: Icon(
                        Icons.save,
                        color: Colors.green[700],
                      ),
                      tooltip: widget.leccionId == null
                          ? 'Crear Lección'
                          : 'Actualizar Lección',
                      onPressed: widget.leccionId == null
                          ? crearLeccion
                          : actualizarLeccion,
                    ),
                    // Botón Guardar Borrador
                    IconButton(
                      icon: Icon(
                        Icons.cloud_upload,
                        color: Colors.orange[700],
                      ),
                      tooltip: 'Guardar Borrador',
                      onPressed: () async {
                        await _guardarBorrador();
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Borrador guardado')),
                        );
                      },
                    ),
                    // Indicador de borrador
                    _buildIndicadorBorrador(),
                  ],
                ),
              ),
              // Tercera fila: Botones de markdown
              Container(
                height: 48.0,
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildMarkdownButton(Icons.format_bold, 'Negrita',
                          () => _insertMarkdown('**', '**')),
                      _buildMarkdownButton(Icons.format_italic, 'Cursiva',
                          () => _insertMarkdown('*', '*')),
                      _buildMarkdownButton(
                          Icons.insert_link, 'Enlace', () => _insertLink()),
                      _buildMarkdownButton(
                          Icons.insert_photo, 'Imagen', () => _abrirGaleria()),
                      _buildMarkdownButton(Icons.format_list_bulleted, 'Lista',
                          () => _insertMarkdown('- ', '')),
                      _buildMarkdownButton(Icons.format_indent_increase,
                          'Indentación', () => _insertMarkdown('  ', '')),
                      _buildMarkdownButton(
                          Icons.title, 'H1', () => _insertMarkdown('# ', '')),
                      _buildMarkdownButton(
                          Icons.title, 'H2', () => _insertMarkdown('## ', '')),
                      _buildMarkdownButton(
                          Icons.title, 'H3', () => _insertMarkdown('### ', '')),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      body: Row(
        children: <Widget>[
          // Panel de edición
          Expanded(
            flex: 1,
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  children: <Widget>[
                    // 1. DROPDOWN DE CATEGORÍAS (Superior)
                    DropdownButtonFormField<int>(
                      value: () {
                        // Validar que el valor seleccionado existe en la lista
                        if (categoriaSeleccionada == null) return null;
                        final existe = categorias.any(
                            (c) => c['categorias'] == categoriaSeleccionada);
                        return existe ? categoriaSeleccionada : null;
                      }(),
                      items: [
                        DropdownMenuItem<int>(
                          value: null,
                          child: Text('Seleccionar Categoría'),
                        ),
                        ...() {
                          // Filtrar categorías válidas y eliminar duplicados
                          final categoriasValidas = <Map<String, dynamic>>[];
                          final idsVistos = <int>{};

                          for (var categoria in categorias) {
                            final categoriaId = categoria['categorias'];
                            if (categoriaId != null &&
                                !idsVistos.contains(categoriaId)) {
                              idsVistos.add(categoriaId);
                              categoriasValidas.add(categoria);
                            }
                          }

                          return categoriasValidas.map((categoria) {
                            return DropdownMenuItem<int>(
                              value: categoria['categorias'],
                              child: Text(
                                '${categoria['categorias']} - ${categoria['categoria_nombre']}',
                                overflow: TextOverflow.ellipsis,
                              ),
                            );
                          });
                        }(),
                        // Opción para crear nueva categoría
                        DropdownMenuItem<int>(
                          value: -998, // Valor especial para nueva categoría
                          child: Row(
                            children: [
                              Icon(Icons.add, color: Colors.blue),
                              SizedBox(width: 8),
                              Text('Nueva categoría...',
                                  style: TextStyle(
                                      color: Colors.blue,
                                      fontWeight: FontWeight.bold)),
                            ],
                          ),
                        ),
                      ],
                      onChanged: (value) async {
                        if (value == -998) {
                          // Mostrar diálogo para crear nueva categoría
                          await _mostrarDialogoNuevaCategoria();
                        } else {
                          setState(() {
                            categoriaSeleccionada = value;
                          });
                          // Cargar materias de la categoría seleccionada
                          if (value != null) {
                            await cargarMaterias(categoriaId: value);
                          }
                        }
                      },
                      decoration: const InputDecoration(
                        labelText: 'CATEGORÍA PRINCIPAL',
                      ),
                    ),

                    // 1.5. DROPDOWN DE SEGUNDA CATEGORÍA
                    DropdownButtonFormField<int>(
                      value: () {
                        // Validar que el valor seleccionado existe en la lista
                        if (categoria2Seleccionada == null) return null;
                        final existe = categorias.any(
                            (c) => c['categorias'] == categoria2Seleccionada);
                        return existe ? categoria2Seleccionada : null;
                      }(),
                      items: [
                        DropdownMenuItem<int>(
                          value: null,
                          child:
                              Text('Seleccionar Segunda Categoría (Opcional)'),
                        ),
                        ...() {
                          // Filtrar categorías válidas y eliminar duplicados
                          final categoriasValidas = <Map<String, dynamic>>[];
                          final idsVistos = <int>{};

                          for (var categoria in categorias) {
                            final categoriaId = categoria['categorias'];
                            if (categoriaId != null &&
                                !idsVistos.contains(categoriaId)) {
                              idsVistos.add(categoriaId);
                              categoriasValidas.add(categoria);
                            }
                          }

                          return categoriasValidas.map((categoria) {
                            return DropdownMenuItem<int>(
                              value: categoria['categorias'],
                              child: Text(
                                '${categoria['categorias']} - ${categoria['categoria_nombre']}',
                                overflow: TextOverflow.ellipsis,
                              ),
                            );
                          });
                        }(),
                        // Opción para crear nueva categoría
                        DropdownMenuItem<int>(
                          value: -998, // Valor especial para nueva categoría
                          child: Row(
                            children: [
                              Icon(Icons.add, color: Colors.blue),
                              SizedBox(width: 8),
                              Text('Nueva categoría...',
                                  style: TextStyle(
                                      color: Colors.blue,
                                      fontWeight: FontWeight.bold)),
                            ],
                          ),
                        ),
                      ],
                      onChanged: (value) async {
                        if (value == -998) {
                          // Mostrar diálogo para crear nueva categoría
                          await _mostrarDialogoNuevaCategoria();
                        } else {
                          setState(() {
                            categoria2Seleccionada = value;
                          });
                        }
                      },
                      decoration: const InputDecoration(
                        labelText: 'SEGUNDA CATEGORÍA',
                        helperText: 'Opcional - Para clasificación adicional',
                      ),
                    ),

                    // 2. DROPDOWN DE MATERIAS (Medio)
                    DropdownButtonFormField<int>(
                      value: () {
                        // Validar que el valor seleccionado existe en la lista
                        if (materiaSeleccionada == null) return null;
                        final existe = materias
                            .any((m) => m['materia_id'] == materiaSeleccionada);
                        return existe ? materiaSeleccionada : null;
                      }(),
                      items: [
                        DropdownMenuItem<int>(
                          value: null,
                          child: Text(categoriaSeleccionada == null
                              ? 'Selecciona una categoría primero'
                              : 'Seleccionar Materia'),
                        ),
                        ...materias.map((materia) {
                          return DropdownMenuItem<int>(
                            value: materia['materia_id'],
                            child: Text(
                              '${materia['materia_id']} - ${materia['materia_nombre']}',
                              overflow: TextOverflow.ellipsis,
                            ),
                          );
                        }),
                        // Opción para crear nueva materia
                        if (categoriaSeleccionada != null)
                          DropdownMenuItem<int>(
                            value: -997, // Valor especial para nueva materia
                            child: Row(
                              children: [
                                Icon(Icons.add, color: Colors.orange),
                                SizedBox(width: 8),
                                Text('Nueva materia...',
                                    style: TextStyle(
                                        color: Colors.orange,
                                        fontWeight: FontWeight.bold)),
                              ],
                            ),
                          ),
                      ],
                      onChanged: categoriaSeleccionada == null
                          ? null
                          : (value) async {
                              if (value == -997) {
                                // Mostrar diálogo para crear nueva materia
                                await _mostrarDialogoNuevaMateria();
                              } else {
                                setState(() {
                                  materiaSeleccionada = value;
                                  // Resetear selección de unidad
                                  unidadSeleccionada = null;
                                });
                                // Cargar unidades cuando se selecciona una materia
                                if (value != null) {
                                  await cargarUnidadesPorMateria(
                                      materiaId: value);
                                }
                              }
                            },
                      decoration: const InputDecoration(
                        labelText: 'MATERIA',
                      ),
                    ),

                    // 3. DROPDOWN DE UNIDADES (Inferior)
                    DropdownButtonFormField<int>(
                      value: () {
                        // Validar que el valor seleccionado existe en la lista
                        if (unidadSeleccionada == null) return null;
                        final existe = unidades
                            .any((u) => u['unidades_id'] == unidadSeleccionada);
                        return existe ? unidadSeleccionada : null;
                      }(),
                      items: [
                        DropdownMenuItem<int>(
                          value: null,
                          child: Text(materiaSeleccionada == null
                              ? 'Selecciona una materia primero'
                              : 'Seleccionar Unidad'),
                        ),
                        ...() {
                          // Filtrar unidades válidas y eliminar duplicados
                          final unidadesValidas = <Map<String, dynamic>>[];
                          final idsVistos = <int>{};

                          for (var unidad in unidades) {
                            final unidadId = unidad['unidades_id'];
                            if (unidadId != null &&
                                !idsVistos.contains(unidadId)) {
                              idsVistos.add(unidadId);
                              unidadesValidas.add(unidad);
                            }
                          }

                          return unidadesValidas.map((unidad) {
                            return DropdownMenuItem<int>(
                              value: unidad['unidades_id'],
                              child: Text(
                                '${unidad['unidades_id']} - ${unidad['unidad_nombre']}',
                                overflow: TextOverflow.ellipsis,
                              ),
                            );
                          });
                        }(),
                        // Opción para crear nueva unidad
                        if (materiaSeleccionada != null)
                          DropdownMenuItem<int>(
                            value: -999, // Valor especial para nueva unidad
                            child: Row(
                              children: [
                                Icon(Icons.add, color: Colors.green),
                                SizedBox(width: 8),
                                Text('Nueva unidad...',
                                    style: TextStyle(
                                        color: Colors.green,
                                        fontWeight: FontWeight.bold)),
                              ],
                            ),
                          ),
                      ],
                      onChanged: materiaSeleccionada == null
                          ? null
                          : (value) async {
                              if (value == -999) {
                                // Mostrar diálogo para crear nueva unidad
                                await _mostrarDialogoNuevaUnidad();
                              } else {
                                setState(() {
                                  unidadSeleccionada = value;
                                });
                              }
                            },
                      decoration: const InputDecoration(
                        labelText: 'UNIDAD',
                      ),
                    ),

                    TextField(
                      controller: tituloController,
                      decoration: const InputDecoration(
                        labelText: 'Título',
                      ),
                    ),
                    DropdownButtonFormField<String>(
                      value: existingSections.contains(seccionController.text)
                          ? seccionController.text
                          : null,
                      items: [
                        DropdownMenuItem<String>(
                          value: null,
                          child: Text('Seleccionar Sección'),
                        ),
                        ...existingSections.map((section) => DropdownMenuItem(
                              value: section,
                              child: Text(section),
                            )),
                        DropdownMenuItem(
                          value: 'new_section',
                          child: Row(
                            children: const [
                              Icon(Icons.add),
                              SizedBox(width: 8),
                              Text('Nueva Sección'),
                            ],
                          ),
                        ),
                      ],
                      onChanged: (value) {
                        if (value == 'new_section') {
                          showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: const Text('Nueva Sección'),
                              content: TextField(
                                controller: TextEditingController(),
                                decoration: const InputDecoration(
                                  labelText: 'Nombre de la sección',
                                ),
                                onSubmitted: (value) {
                                  setState(() {
                                    seccionController.text = value;
                                    if (!existingSections.contains(value)) {
                                      existingSections.add(value);
                                    }
                                  });
                                  Navigator.pop(context);
                                },
                              ),
                            ),
                          );
                        } else {
                          setState(() {
                            seccionController.text = value ?? '';
                          });
                        }
                      },
                      decoration: const InputDecoration(
                        labelText: 'Sección',
                      ),
                    ),
                    TextField(
                      controller: creadorController,
                      decoration: const InputDecoration(
                        labelText: 'Creador',
                        enabled: false,
                      ),
                    ),
                    TextField(
                      controller: editorController,
                      decoration: const InputDecoration(
                        labelText: 'Editor',
                        enabled: false,
                      ),
                    ),
                    SizedBox(
                      height: 450,
                      child: ExtendedTextField(
                        controller: contenidoController,
                        maxLines: null,
                        specialTextSpanBuilder: ReferenciaSpanBuilder(
                          leccionId:
                              widget.leccionId ?? -1, // -1 para nueva lección
                          context: context,
                          diccionarioCache: diccionarioCache,
                          textController: contenidoController,
                        ),
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Panel lateral (solo si hay un panel activo)       // Panel lateral (solo si hay un panel activo)
          if (_activePanel.isNotEmpty)
            Container(
              width: 400,
              color: Colors.white,
              child: _activePanel == 'referencias'
                  ? _buildReferenciasPanel()
                  : _activePanel == 'imagenes'
                      ? _buildImagenesActivas()
                      : _activePanel == 'definiciones'
                          ? _buildDefinicionesPanel()
                          : _activePanel == 'cuadros'
                              ? _buildCuadrosPanel()
                              : _activePanel == 'lecciones'
                                  ? _buildLeccionesPanel()
                                  : _activePanel == 'markdown'
                                      ? _buildMarkdownPanel()
                                      : _activePanel == 'admin'
                                          ? _buildAdminPanel()
                                          : Container(), // Panel vacío si no hay panel activo
            ),
          // Solo mostrar el área de vista previa cuando el panel de markdown NO esté activo
          // Cuando el panel está activo, se muestra en el lateral
        ],
      ),
    );
  }

  Widget _buildLeccionesPanel() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: TextField(
            controller: leccionSearchController,
            decoration: const InputDecoration(
              labelText: 'Buscar lección',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              _buscarLeccionesWithDebounce(value);
            },
          ),
        ),
        Expanded(
          child: ValueListenableBuilder<List<Map<String, dynamic>>>(
            valueListenable: leccionesBusquedaNotifier,
            builder: (context, lecciones, _) {
              if (lecciones.isEmpty) {
                return const Center(
                    child: Text('No hay lecciones disponibles'));
              }
              return ListView.builder(
                itemCount: lecciones.length,
                itemBuilder: (context, index) {
                  final leccion = lecciones[index];
                  final leccionNombre =
                      leccion['leccion_nombre'] ?? 'Sin nombre';
                  return Card(
                    margin: const EdgeInsets.symmetric(
                        horizontal: 8.0, vertical: 4.0),
                    child: ListTile(
                      title: Text(leccionNombre),
                      trailing: IconButton(
                        icon: const Icon(Icons.input, color: Colors.deepPurple),
                        tooltip: 'Insertar referencia en texto',
                        onPressed: () {
                          final seleccion = contenidoController.selection;
                          final texto = contenidoController.text;

                          String displayName;
                          if (!seleccion.isCollapsed) {
                            displayName =
                                texto.substring(seleccion.start, seleccion.end);
                          } else {
                            displayName = leccionNombre;
                          }

                          final referencia = '($displayName)[$leccionNombre]';

                          final nuevoTexto = texto.replaceRange(
                            seleccion.start,
                            seleccion.end,
                            referencia,
                          );

                          setState(() {
                            contenidoController.text = nuevoTexto;
                            final nuevaPosicion =
                                seleccion.start + referencia.length;
                            contenidoController.selection =
                                TextSelection.collapsed(offset: nuevaPosicion);
                          });

                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                  'Referencia a "$leccionNombre" insertada como "$displayName"'),
                            ),
                          );
                        },
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildDefinicionesActivas() {
    // Extraer las definiciones referenciadas en el texto
    final texto = contenidoController.text;
    final RegExp definicionExp = RegExp(r'\[([^\]]+)\]\(#[^)]+\)');
    final matches = definicionExp.allMatches(texto);

    // Crear un Set para evitar duplicados
    final Set<String> definicionesUnicas = {};
    for (final match in matches) {
      final nombreDefinicion = match.group(1);
      if (nombreDefinicion != null) {
        definicionesUnicas.add(nombreDefinicion);
      }
    }

    return Column(
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12.0),
          color: Colors.green[50],
          child: Text(
            'Definiciones activas en esta lección (${definicionesUnicas.length})',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.green,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        Expanded(
          child: definicionesUnicas.isEmpty
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Text(
                      'No hay definiciones en esta lección',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                )
              : _buildListaDefinicionesActivas(definicionesUnicas),
        ),
      ],
    );
  }

  Widget _buildListaDefinicionesActivas(Set<String> definicionesUnicas) {
    return ListView.builder(
      itemCount: definicionesUnicas.length,
      itemBuilder: (context, index) {
        final nombreDefinicion = definicionesUnicas.elementAt(index);
        final definicionContenido =
            diccionarioCache[nombreDefinicion.toLowerCase()] ??
                'Definición no encontrada';

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
          child: ListTile(
            leading: const Icon(Icons.check_circle, color: Colors.green),
            title: Text(
              nombreDefinicion,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(
              definicionContenido.length > 100
                  ? '${definicionContenido.substring(0, 100)}...'
                  : definicionContenido,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.visibility, color: Colors.blue),
                  tooltip: 'Ver definición completa',
                  onPressed: () {
                    bs.showBottomSheet(context, nombreDefinicion.toLowerCase(),
                        diccionarioCache);
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.remove_circle, color: Colors.red),
                  tooltip: 'Quitar definición del texto',
                  onPressed: () => _quitarDefinicionDelTexto(nombreDefinicion),
                ),
                IconButton(
                  icon: const Icon(Icons.add_circle, color: Colors.green),
                  tooltip: 'Insertar definición en cursor',
                  onPressed: () =>
                      _insertarDefinicionEnCursor(nombreDefinicion),
                ),
              ],
            ),
            onTap: () {
              bs.showBottomSheet(
                  context, nombreDefinicion.toLowerCase(), diccionarioCache);
            },
          ),
        );
      },
    );
  }

  void _quitarDefinicionDelTexto(String nombreDefinicion) {
    final texto = contenidoController.text;

    // Buscar todas las referencias a esta definición en el texto
    final RegExp definicionExp =
        RegExp(r'\[' + RegExp.escape(nombreDefinicion) + r'\]\(#[^)]+\)');
    final matches = definicionExp.allMatches(texto).toList();

    if (matches.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(
                'No se encontraron referencias a "$nombreDefinicion" en el texto')),
      );
      return;
    }

    // Mostrar diálogo de confirmación
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Quitar definición del texto'),
        content: Text(
            'Se encontraron ${matches.length} referencia(s) a "$nombreDefinicion" en el texto.\n\n'
            '¿Deseas eliminar todas las referencias?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () {
              // Eliminar todas las referencias a la definición
              final nuevoTexto = texto.replaceAll(definicionExp, '');
              setState(() {
                contenidoController.text = nuevoTexto;
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                    content: Text(
                        'Se eliminaron ${matches.length} referencia(s) a "$nombreDefinicion"')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Eliminar'),
          ),
        ],
      ),
    );
  }

  void _insertarDefinicionEnCursor(String nombreDefinicion) {
    final cursorPos = contenidoController.selection.baseOffset;
    final texto = contenidoController.text;

    // Crear el link de la definición
    final linkDefinicion =
        '[$nombreDefinicion](#${nombreDefinicion.toLowerCase().replaceAll(' ', '_')})';

    // Insertar la definición en la posición del cursor
    final nuevoTexto = texto.replaceRange(
      cursorPos,
      cursorPos,
      linkDefinicion,
    );

    setState(() {
      contenidoController.text = nuevoTexto;
      // Mover el cursor después de la definición insertada
      final nuevaPosicion = cursorPos + linkDefinicion.length;
      contenidoController.selection =
          TextSelection.collapsed(offset: nuevaPosicion);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
          content:
              Text('Definición "$nombreDefinicion" insertada en el cursor')),
    );
  }

  // Método para crear botones de markdown en el AppBar
  Widget _buildMarkdownButton(
      IconData icon, String tooltip, VoidCallback onPressed) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 2.0),
      child: IconButton(
        icon: Icon(icon, size: 20),
        tooltip: tooltip,
        onPressed: onPressed,
        constraints: const BoxConstraints(
          minWidth: 32,
          minHeight: 32,
        ),
      ),
    );
  }

  // Método para insertar markdown genérico
  void _insertMarkdown(String prefix, String suffix) {
    final currentText = contenidoController.text;
    final selection = contenidoController.selection;
    final selectedText = selection.textInside(currentText);

    if (selectedText.isNotEmpty && suffix.isNotEmpty) {
      // Si hay texto seleccionado y hay sufijo, envolver el texto
      final newText = currentText.replaceRange(
        selection.start,
        selection.end,
        '$prefix$selectedText$suffix',
      );
      contenidoController.text = newText;
      contenidoController.selection = selection.copyWith(
        baseOffset: selection.start + prefix.length,
        extentOffset: selection.end + prefix.length,
      );
    } else {
      // Si no hay texto seleccionado o no hay sufijo, insertar solo el prefijo
      final newText = currentText.replaceRange(
        selection.start,
        selection.start,
        prefix,
      );
      contenidoController.text = newText;
      contenidoController.selection = selection.copyWith(
        baseOffset: selection.start + prefix.length,
        extentOffset: selection.start + prefix.length,
      );
    }
  }

  // Método para insertar enlaces
  void _insertLink() {
    final currentText = contenidoController.text;
    final selection = contenidoController.selection;
    final selectedText = selection.textInside(currentText);

    if (selectedText.isNotEmpty) {
      final newText = currentText.replaceRange(
        selection.start,
        selection.end,
        '[$selectedText](#$selectedText)',
      );
      contenidoController.text = newText;
      contenidoController.selection = selection.copyWith(
        baseOffset: selection.start + 1,
        extentOffset: selection.start + 1 + selectedText.length,
      );
    }
  }

  // Panel de administración con scripts de diccionario
  Widget _buildAdminPanel() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Título del panel
          Row(
            children: [
              Icon(Icons.settings, color: Colors.blue, size: 24),
              SizedBox(width: 8),
              Text(
                'Administración de Diccionarios',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
          SizedBox(height: 16),

          // Información del estado actual
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              border: Border.all(color: Colors.blue.shade200),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '📊 Estado del Diccionario',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                ),
                SizedBox(height: 8),
                FutureBuilder<Map<String, int>>(
                  future: _obtenerEstadisticasDiccionario(),
                  builder: (context, snapshot) {
                    final stats =
                        snapshot.data ?? {'base': 0, 'personalizadas': 0};
                    final palabrasBase = stats['base'] ?? 0;
                    final palabrasPersonalizadas = stats['personalizadas'] ?? 0;

                    return Text(
                      '• Archivo base: diccionario_completo_final.json\n'
                      '• Palabras en archivo: ${palabrasBase.toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')} palabras\n'
                      '• Palabras pendientes: $palabrasPersonalizadas\n'
                      '• Estado caché: ${diccionarioCache.isNotEmpty ? "Cargado" : "Vacío"}',
                      style: TextStyle(color: Colors.blue.shade600),
                    );
                  },
                ),
              ],
            ),
          ),
          SizedBox(height: 16),

          // Instrucciones para integrar palabras
          Text(
            '📝 Integración de Palabras',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          SizedBox(height: 12),

          // Información sobre integración manual
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue),
                      SizedBox(width: 8),
                      Text(
                        'Integración Manual',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Para integrar palabras al diccionario base:\n\n'
                    '1. Cierra la aplicación\n'
                    '2. Abre terminal en: scripts/\n'
                    '3. Ejecuta: dart actualizar_diccionario.dart\n'
                    '4. Reabre la aplicación\n\n'
                    'Esto agregará las palabras nuevas al archivo base.',
                    style: TextStyle(color: Colors.grey.shade600),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Obtener estadísticas completas del diccionario
  Future<Map<String, int>> _obtenerEstadisticasDiccionario() async {
    try {
      // 1. Contar palabras en archivo base
      final String contenidoBase = await DefaultAssetBundle.of(context)
          .loadString('assets/diccionario_completo_final.json');
      final Map<String, dynamic> dataBase = json.decode(contenidoBase);
      final List<dynamic> palabrasBase = dataBase['palabras'] ?? [];

      // 2. Contar palabras personalizadas pendientes
      final prefs = await SharedPreferences.getInstance();
      final List<String> palabrasPersonalizadas =
          prefs.getStringList('diccionario_personalizado') ?? [];

      return {
        'base': palabrasBase.length,
        'personalizadas': palabrasPersonalizadas.length,
      };
    } catch (e) {
      return {'base': 0, 'personalizadas': 0};
    }
  }

  // ==================== SISTEMA DE BORRADOR ====================

  // Inicializar sistema de borrador
  Future<void> _inicializarBorrador() async {
    if (widget.leccionId == null) return;

    // Cargar borrador existente
    await _cargarBorrador();

    // Iniciar auto-guardado
    _iniciarAutoGuardado();
  }

  // Cargar borrador existente automáticamente
  Future<void> _cargarBorrador() async {
    // Solo cargar borrador si estamos editando una lección existente
    if (widget.leccionId == null) return;

    try {
      final response = await Supabase.instance.client
          .from('lecciones_borradores')
          .select()
          .eq('leccion_id', widget.leccionId!)
          .maybeSingle();

      if (response != null) {
        // Cargar borrador automáticamente sin preguntar
        _restaurarBorrador(response);
        logger.i('✅ Borrador cargado automáticamente');
      } else {
        // Si no hay borrador, cargar contenido original de la lección
        _cargarContenidoOriginal();
        logger.i('📄 No hay borrador, cargando contenido original');
      }
    } catch (e) {
      logger.e('Error cargando borrador: $e');
      // En caso de error, cargar contenido original
      _cargarContenidoOriginal();
    }
  }

  // Cargar contenido original de la lección
  void _cargarContenidoOriginal() {
    if (widget.leccionContenido != null) {
      contenidoController.text = widget.leccionContenido!;
    }
  }

  // Restaurar borrador
  void _restaurarBorrador(Map<String, dynamic> borrador) {
    setState(() {
      if (borrador['titulo_borrador'] != null) {
        tituloController.text = borrador['titulo_borrador'];
      }
      if (borrador['contenido_borrador'] != null) {
        contenidoController.text = borrador['contenido_borrador'];
      }
      if (borrador['seccion_borrador'] != null) {
        seccionController.text = borrador['seccion_borrador'];
      }
      if (borrador['categoria_id_borrador'] != null) {
        categoriaSeleccionada = borrador['categoria_id_borrador'];
      }
      if (borrador['unidad_id_borrador'] != null) {
        unidadSeleccionada = borrador['unidad_id_borrador'];
      }
    });
  }

  // Eliminar borrador después de guardar la lección definitivamente
  Future<void> _eliminarBorradorDespuesDeGuardar() async {
    if (widget.leccionId == null) return;

    try {
      await Supabase.instance.client
          .from('lecciones_borradores')
          .delete()
          .eq('leccion_id', widget.leccionId!);

      logger.i('🗑️ Borrador eliminado después de guardar lección');

      // Cancelar auto-guardado ya que la lección está guardada
      _autoSaveTimer?.cancel();

      _borradorGuardadoNotifier.value =
          true; // Marcar como guardado para evitar confusión
    } catch (e) {
      logger.e('Error eliminando borrador después de guardar: $e');
      // No es crítico si falla, la lección ya se guardó
    }
  }

  // Iniciar auto-guardado de borrador (menos frecuente)
  void _iniciarAutoGuardado() {
    _autoSaveTimer?.cancel();
    // Reducir frecuencia para mejorar rendimiento
    _autoSaveTimer = Timer.periodic(Duration(minutes: 2), (timer) {
      _guardarBorrador();
    });
  }

  // Guardar borrador en la nube
  Future<void> _guardarBorrador() async {
    if (widget.leccionId == null) return;

    try {
      _borradorGuardadoNotifier.value = false;

      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) return;

      await Supabase.instance.client.from('lecciones_borradores').upsert({
        'leccion_id': widget.leccionId!,
        'contenido_borrador': contenidoController.text,
        'titulo_borrador': tituloController.text,
        'seccion_borrador': seccionController.text,
        'categoria_id_borrador': categoriaSeleccionada,
        'unidad_id_borrador': unidadSeleccionada,
        'ultimo_editor_id': user.id,
        'ultimo_editor_nombre':
            user.userMetadata?['full_name'] ?? user.email ?? 'Usuario',
      }, onConflict: 'leccion_id');

      _borradorGuardadoNotifier.value = true;
    } catch (e) {
      logger.e('Error guardando borrador: $e');
    }
  }

  // Indicador visual del estado del borrador
  Widget _buildIndicadorBorrador() {
    return ValueListenableBuilder<bool>(
      valueListenable: _borradorGuardadoNotifier,
      builder: (context, borradorGuardado, child) {
        return Container(
          margin: EdgeInsets.only(left: 8),
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: borradorGuardado ? Colors.green[100] : Colors.orange[100],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color:
                  borradorGuardado ? Colors.green[300]! : Colors.orange[300]!,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                borradorGuardado ? Icons.cloud_done : Icons.cloud_upload,
                size: 16,
                color:
                    borradorGuardado ? Colors.green[700] : Colors.orange[700],
              ),
              SizedBox(width: 4),
              Text(
                borradorGuardado ? 'Guardado' : 'Guardando...',
                style: TextStyle(
                  fontSize: 12,
                  color:
                      borradorGuardado ? Colors.green[700] : Colors.orange[700],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Agregar listeners para auto-guardado (OPTIMIZADO)
  void _agregarListenersAutoGuardado() {
    // Listener para el contenido (optimizado)
    contenidoController.addListener(_onTextChanged);

    // Listener para el título (optimizado)
    tituloController.addListener(_onTextChanged);

    // Listener para la sección (optimizado)
    seccionController.addListener(_onTextChanged);
  }

  // Método optimizado para cambios de texto
  void _onTextChanged() {
    // Solo cambiar estado si es necesario
    if (_borradorGuardadoNotifier.value) {
      _borradorGuardadoNotifier.value = false;
    }

    // Programar auto-guardado con debounce
    _programarAutoGuardado();
  }

  // Programar auto-guardado con delay optimizado
  Timer? _delayedSaveTimer;
  void _programarAutoGuardado() {
    _delayedSaveTimer?.cancel();
    // Aumentar delay para reducir llamadas frecuentes
    _delayedSaveTimer = Timer(Duration(seconds: 5), () {
      _guardarBorrador();
    });
  }

  // Limpiar timers al cerrar
  @override
  void dispose() {
    _autoSaveTimer?.cancel();
    _delayedSaveTimer?.cancel();
    _searchDebounceTimer?.cancel();
    _searchDefinicionDebounceTimer?.cancel();
    _borradorGuardadoNotifier.dispose();
    leccionesBusquedaNotifier.dispose();
    definicionesBusquedaNotifier.dispose();
    super.dispose();
  }

  // Panel de vista previa de Markdown
  Widget _buildMarkdownPanel() {
    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Encabezado del panel
          Container(
            padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.purple.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.purple.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.visibility, color: Colors.purple.shade700),
                SizedBox(width: 8),
                Text(
                  'Vista Previa Markdown',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.purple.shade700,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 16),

          // Contenido de la vista previa
          Expanded(
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: _MarkdownPreviewOptimized(
                contenidoController: contenidoController,
                referenciasBanco: referenciasBanco,
                procesarReferencias: procesarReferenciasPreview,
              ),
            ),
          ),

          SizedBox(height: 16),

          // Información adicional
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue.shade700, size: 20),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Esta vista previa se actualiza automáticamente mientras escribes',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Función para mostrar diálogo de nueva unidad
  Future<void> _mostrarDialogoNuevaUnidad() async {
    final nombreController = TextEditingController();
    final descripcionController = TextEditingController();

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.add, color: Colors.green),
            SizedBox(width: 8),
            Text('Nueva Unidad'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nombreController,
                decoration: InputDecoration(
                  labelText: 'Nombre de la unidad',
                  border: OutlineInputBorder(),
                ),
                autofocus: true,
              ),
              SizedBox(height: 16),
              TextField(
                controller: descripcionController,
                decoration: InputDecoration(
                  labelText: 'Descripción (opcional)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (nombreController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('El nombre es obligatorio')),
                );
                return;
              }
              Navigator.pop(context, true);
            },
            child: Text('Crear'),
          ),
        ],
      ),
    );

    if (result == true) {
      await _crearNuevaUnidad(
          nombreController.text.trim(), descripcionController.text.trim());
    }
  }

  // Función para crear nueva unidad en la base de datos
  Future<void> _crearNuevaUnidad(String nombre, String descripcion) async {
    if (materiaSeleccionada == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Debe seleccionar una materia primero')),
      );
      return;
    }

    try {
      final response = await Supabase.instance.client
          .from('unidades')
          .insert({
            'unidad_nombre': nombre,
            'unidad_descripcion': descripcion.isEmpty ? null : descripcion,
            'materia_id': materiaSeleccionada,
          })
          .select()
          .single();

      final nuevaUnidadId = response['unidades_id'];

      // Recargar la lista de unidades filtrada por materia
      await cargarUnidadesPorMateria(materiaId: materiaSeleccionada);

      // Seleccionar automáticamente la nueva unidad
      setState(() {
        unidadSeleccionada = nuevaUnidadId;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Unidad "$nombre" creada exitosamente'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      logger.e('Error creando nueva unidad: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al crear la unidad: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Función para mostrar diálogo de nueva categoría
  Future<void> _mostrarDialogoNuevaCategoria() async {}

  // Función para mostrar diálogo de nueva materia
  Future<void> _mostrarDialogoNuevaMateria() async {
    final nombreController = TextEditingController();
    final descripcionController = TextEditingController();

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.add, color: Colors.orange),
            SizedBox(width: 8),
            Text('Nueva Materia'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nombreController,
                decoration: InputDecoration(
                  labelText: 'Nombre de la materia',
                  border: OutlineInputBorder(),
                ),
                autofocus: true,
              ),
              SizedBox(height: 16),
              TextField(
                controller: descripcionController,
                decoration: InputDecoration(
                  labelText: 'Descripción (opcional)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (nombreController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('El nombre es obligatorio')),
                );
                return;
              }
              Navigator.pop(context, true);
            },
            child: Text('Crear'),
          ),
        ],
      ),
    );

    if (result == true) {
      await _crearNuevaMateria(
          nombreController.text.trim(), descripcionController.text.trim());
    }
  }

  // Función para crear nueva materia en la base de datos
  Future<void> _crearNuevaMateria(String nombre, String descripcion) async {
    if (categoriaSeleccionada == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Debe seleccionar una categoría primero')),
      );
      return;
    }

    try {
      final response = await Supabase.instance.client
          .from('materias')
          .insert({
            'materia_nombre': nombre,
            'materia_descripcion': descripcion.isEmpty ? null : descripcion,
            'categoria_id': categoriaSeleccionada,
          })
          .select()
          .single();

      final nuevaMateriaId = response['materia_id'];

      // Recargar la lista de materias
      await cargarMaterias(categoriaId: categoriaSeleccionada);

      // Seleccionar automáticamente la nueva materia
      setState(() {
        materiaSeleccionada = nuevaMateriaId;
      });

      // Cargar unidades de la nueva materia
      await cargarUnidadesPorMateria(materiaId: nuevaMateriaId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Materia "$nombre" creada exitosamente'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      logger.e('Error creando nueva materia: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al crear la materia: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

class ReferenciaSpanBuilder extends SpecialTextSpanBuilder {
  final int leccionId;
  final BuildContext context;
  final Map<String, String> diccionarioCache;
  final TextEditingController? textController;
  final Set<String> _diccionarioEspanol = {};
  final Set<String> _diccionarioPersonalizado = {};
  bool _diccionarioCargado = false;

  ReferenciaSpanBuilder({
    required this.leccionId,
    required this.context,
    required this.diccionarioCache,
    this.textController,
  }) {
    _cargarDiccionarioEspanol();
    _cargarDiccionarioPersonalizado();
  }

  void _abrirVisorCuadro(String cuadroId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CuadroViewer(cuadroId: cuadroId),
      ),
    );
  }

  Future<void> _cargarDiccionarioEspanol() async {
    if (_diccionarioCargado) return;

    try {
      final prefs = await SharedPreferences.getInstance();

      // Verificar si ya tenemos el diccionario base en JSON
      final diccionarioJson =
          prefs.getString('diccionario_espanol_expandido_final_v1');

      if (diccionarioJson != null) {
        // Cargar solo el diccionario base (rápido)
        await _cargarDesdeJson(diccionarioJson);
      } else {
        // Primera vez: cargar diccionario expandido desde JSON
        // Cargando diccionario expandido
        await _cargarDiccionarioExpandido(prefs);
      }

      _diccionarioCargado = true;
    } catch (e) {
      // Error cargando diccionario - continuar sin corrector
    }
  }

  Future<void> _cargarDesdeJson(String jsonString) async {
    try {
      final Map<String, dynamic> data = json.decode(jsonString);
      final List<dynamic> palabras = data['palabras'] ?? [];
      _diccionarioEspanol.addAll(palabras.cast<String>());
    } catch (e) {
      // Error parseando JSON - continuar sin diccionario
    }
  }

  Future<void> _cargarDiccionarioExpandido(SharedPreferences prefs) async {
    try {
      String contenido;

      // 1. Intentar cargar diccionario actualizado desde SharedPreferences
      final diccionarioActualizado = prefs.getString('diccionario_json');

      if (diccionarioActualizado != null) {
        // Usar diccionario actualizado con palabras integradas
        contenido = diccionarioActualizado;
        // Cargando diccionario actualizado desde caché
      } else {
        // Usar diccionario base desde assets
        contenido = await DefaultAssetBundle.of(context)
            .loadString('assets/diccionario_completo_final.json');
        // Cargando diccionario base desde assets
      }

      final Map<String, dynamic> data = json.decode(contenido);
      final List<dynamic> palabras = data['palabras'] ?? [];

      _diccionarioEspanol.addAll(palabras.cast<String>());

      // Guardar en SharedPreferences para próximas veces
      await prefs.setString(
          'diccionario_espanol_expandido_final_v2', contenido);

      // Diccionario expandido cargado exitosamente
    } catch (e) {
      // Error cargando diccionario expandido - usar fallback
      await _crearDiccionarioBase(prefs);
    }
  }

  Future<void> _crearDiccionarioBase(SharedPreferences prefs) async {
    try {
      // Cargar desde el diccionario Hunspell .dic
      final String contenido = await DefaultAssetBundle.of(context)
          .loadString('assets/es_words.dic');

      // Procesar formato Hunspell y expandir formas
      final Set<String> palabrasExpandidas = {};
      final lineas = contenido.split('\n');

      for (final linea in lineas) {
        final lineaLimpia = linea.trim().toLowerCase();
        if (lineaLimpia.isEmpty || lineaLimpia.startsWith('#')) continue;

        if (lineaLimpia.contains('/')) {
          // Formato Hunspell: "animal/S"
          final partes = lineaLimpia.split('/');
          final palabraBase = partes[0];
          final reglas = partes.length > 1 ? partes[1] : '';

          // Agregar palabra base
          palabrasExpandidas.add(palabraBase);

          // Expandir según reglas
          palabrasExpandidas
              .addAll(_expandirPalabraHunspell(palabraBase, reglas));
        } else {
          // Palabra simple sin reglas
          palabrasExpandidas.add(lineaLimpia);
        }
      }

      // Procesamiento completado

      final List<String> palabrasOriginales = palabrasExpandidas.toList();

      // Solo cargar palabras base (SIN variaciones masivas)
      final Set<String> palabrasBase = {};

      for (final palabra in palabrasOriginales) {
        palabrasBase.add(palabra);
      }

      _diccionarioEspanol.addAll(palabrasBase);

      // Guardar como JSON base (mucho más pequeño)
      final diccionarioBase = {
        'version': 'rae_base_v1',
        'fuente': 'Real Academia Española (RAE) - Solo base',
        'fecha_creacion': DateTime.now().toIso8601String(),
        'palabras': palabrasBase.toList(),
        'total_palabras': palabrasBase.length,
      };

      await prefs.setString('diccionario_espanol_expandido_final_v1',
          json.encode(diccionarioBase));
    } catch (e) {
      // Error creando diccionario base - continuar sin diccionario
    }
  }

  // Expandir palabra según reglas de Hunspell
  List<String> _expandirPalabraHunspell(String palabraBase, String reglas) {
    final formas = <String>[];

    for (int i = 0; i < reglas.length; i++) {
      final regla = reglas[i];

      switch (regla) {
        case 'S': // Plural simple: agregar "s"
          formas.add('${palabraBase}s');
          break;

        case 'E': // Plural con "es"
          formas.add('${palabraBase}es');
          break;

        case 'M': // Género: cambiar "o" por "a"
          if (palabraBase.endsWith('o')) {
            final raiz = palabraBase.substring(0, palabraBase.length - 1);
            formas.add('${raiz}a'); // médico → médica
          }
          break;

        case 'N': // Plural especial para palabras terminadas en consonante
          if (!palabraBase.endsWith('s') && !palabraBase.endsWith('n')) {
            formas.add('${palabraBase}es');
          }
          break;

        case 'Z': // Cambio z → ces
          if (palabraBase.endsWith('z')) {
            final raiz = palabraBase.substring(0, palabraBase.length - 1);
            formas.add('${raiz}ces'); // luz → luces
          }
          break;
      }
    }

    // Para combinaciones como "SM" (plural + género)
    if (reglas.contains('S') && reglas.contains('M')) {
      // Si tiene tanto S como M, generar todas las combinaciones
      if (palabraBase.endsWith('o')) {
        final raiz = palabraBase.substring(0, palabraBase.length - 1);
        formas.add('${raiz}os'); // médico → médicos
        formas.add('${raiz}as'); // médico → médicas
      }
    }

    return formas;
  }

  Future<void> _cargarDiccionarioPersonalizado() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final palabrasPersonalizadas =
          prefs.getStringList('diccionario_personalizado') ?? [];
      _diccionarioPersonalizado
          .addAll(palabrasPersonalizadas.map((p) => p.toLowerCase()));
    } catch (e) {
      // Error cargando diccionario personalizado - continuar sin él
    }
  }

  Future<void> _agregarPalabraAlDiccionario(String palabra) async {
    try {
      final palabraLimpia = palabra.toLowerCase().trim();
      if (palabraLimpia.isNotEmpty &&
          !_diccionarioPersonalizado.contains(palabraLimpia)) {
        _diccionarioPersonalizado.add(palabraLimpia);

        final prefs = await SharedPreferences.getInstance();
        await prefs.setStringList(
            'diccionario_personalizado', _diccionarioPersonalizado.toList());
      }
    } catch (e) {
      // Error agregando palabra - continuar silenciosamente
    }
  }

  // Método para forzar recarga del texto con corrector ortográfico
  void recargarCorrectorOrtografico() {
    if (_diccionarioCargado) {
      // Trigger a rebuild of the text field
    }
  }

  // Calcular distancia de Levenshtein entre dos palabras

  // Obtener sugerencias para una palabra mal escrita
  List<String> _obtenerSugerencias(String palabraIncorrecta) {
    if (!_diccionarioCargado || palabraIncorrecta.length < 2) {
      return [];
    }

    final palabraLimpia = palabraIncorrecta.toLowerCase();

    // ALGORITMO MEJORADO - Priorizar casos comunes
    final sugerenciasValidas = <String>[];

    // 1. PRIORIDAD ALTA: Variaciones de acentos (Via → Vía)
    final variacionesAcentos = _generarVariacionesAcentos(palabraLimpia);
    for (final variacion in variacionesAcentos) {
      if (_diccionarioEspanol.contains(variacion) ||
          _diccionarioPersonalizado.contains(variacion)) {
        sugerenciasValidas.add(variacion);
      }
    }

    // 2. PRIORIDAD ALTA: Plurales/Singulares (animales → animal)
    final variacionesPlurales = _generarVariacionesPlurales(palabraLimpia);
    for (final variacion in variacionesPlurales) {
      if (_diccionarioEspanol.contains(variacion) ||
          _diccionarioPersonalizado.contains(variacion)) {
        if (!sugerenciasValidas.contains(variacion)) {
          sugerenciasValidas.add(variacion);
        }
      }
    }

    // 3. Si no hay suficientes, usar algoritmo de Norvig
    if (sugerenciasValidas.length < 3) {
      final candidatos = _generarEdiciones1(palabraLimpia);
      for (final candidato in candidatos) {
        if (_diccionarioEspanol.contains(candidato) ||
            _diccionarioPersonalizado.contains(candidato)) {
          if (!sugerenciasValidas.contains(candidato)) {
            sugerenciasValidas.add(candidato);
          }
        }
        if (sugerenciasValidas.length >= 5) break;
      }
    }

    // Ordenar por longitud (las más cortas primero, suelen ser mejores)
    sugerenciasValidas.sort((a, b) => a.length.compareTo(b.length));

    return sugerenciasValidas.take(5).toList();
  }

  // ALGORITMO DE NORVIG: Generar todas las ediciones de distancia 1
  Set<String> _generarEdiciones1(String palabra) {
    final ediciones = <String>{};
    final letras = 'abcdefghijklmnopqrstuvwxyzáéíóúüñ';

    // 1. ELIMINACIONES: quitar una letra
    for (int i = 0; i < palabra.length; i++) {
      ediciones.add(palabra.substring(0, i) + palabra.substring(i + 1));
    }

    // 2. TRANSPOSICIONES: intercambiar letras adyacentes
    for (int i = 0; i < palabra.length - 1; i++) {
      ediciones.add(palabra.substring(0, i) +
          palabra[i + 1] +
          palabra[i] +
          palabra.substring(i + 2));
    }

    // 3. REEMPLAZOS: cambiar una letra
    for (int i = 0; i < palabra.length; i++) {
      for (final letra in letras.split('')) {
        ediciones
            .add(palabra.substring(0, i) + letra + palabra.substring(i + 1));
      }
    }

    // 4. INSERCIONES: agregar una letra
    for (int i = 0; i <= palabra.length; i++) {
      for (final letra in letras.split('')) {
        ediciones.add(palabra.substring(0, i) + letra + palabra.substring(i));
      }
    }

    return ediciones;
  }

  // Generar variaciones de acentos (Via → Vía, medico → médico)
  List<String> _generarVariacionesAcentos(String palabra) {
    final variaciones = <String>[];
    final mapaAcentos = {
      'a': 'á',
      'e': 'é',
      'i': 'í',
      'o': 'ó',
      'u': 'ú',
      'á': 'a',
      'é': 'e',
      'í': 'i',
      'ó': 'o',
      'ú': 'u'
    };

    for (int i = 0; i < palabra.length; i++) {
      final char = palabra[i];
      if (mapaAcentos.containsKey(char)) {
        final nuevaPalabra = palabra.substring(0, i) +
            mapaAcentos[char]! +
            palabra.substring(i + 1);
        variaciones.add(nuevaPalabra);
      }
    }

    return variaciones;
  }

  // Generar variaciones de plurales/singulares (animales → animal)
  List<String> _generarVariacionesPlurales(String palabra) {
    final variaciones = <String>[];

    // PLURALES → SINGULARES
    if (palabra.endsWith('es')) {
      // animales → animal, superiores → superior
      final singular = palabra.substring(0, palabra.length - 2);
      variaciones.add(singular);

      // Casos especiales: meses → mes
      if (singular.endsWith('s')) {
        variaciones.add(singular.substring(0, singular.length - 1));
      }
    } else if (palabra.endsWith('s') && palabra.length > 3) {
      // casas → casa, años → año
      variaciones.add(palabra.substring(0, palabra.length - 1));
    }

    // SINGULARES → PLURALES
    if (!palabra.endsWith('s')) {
      variaciones.add('${palabra}s'); // casa → casas
      variaciones.add('${palabra}es'); // animal → animales

      // Casos especiales
      if (palabra.endsWith('z')) {
        variaciones.add(
            '${palabra.substring(0, palabra.length - 1)}ces'); // luz → luces
      }
    }

    return variaciones;
  }

  // Obtener candidatos inteligentes para sugerencias (mucho más rápido)

  // Mostrar menú contextual para una palabra mal escrita
  void _mostrarMenuContextual(String palabraIncorrecta, Offset clickPosition) {
    final sugerencias = _obtenerSugerencias(palabraIncorrecta);

    // Usar la posición exacta del click
    showMenu<String>(
      context: context,
      position: RelativeRect.fromLTRB(
        clickPosition.dx,
        clickPosition.dy,
        clickPosition.dx + 1,
        clickPosition.dy + 1,
      ),
      useRootNavigator: false, // Evitar problemas de scroll
      items: [
        PopupMenuItem<String>(
          enabled: false,
          child: Text(
            sugerencias.isNotEmpty
                ? 'Sugerencias para "$palabraIncorrecta"'
                : 'No hay sugerencias para "$palabraIncorrecta"',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
        ),
        if (sugerencias.isNotEmpty) ...[
          const PopupMenuDivider(),
          ...sugerencias.map(
            (sugerencia) => PopupMenuItem<String>(
              value: sugerencia,
              child: Text(sugerencia),
            ),
          ),
        ],
        const PopupMenuDivider(),
        PopupMenuItem<String>(
          value: '__AGREGAR_AL_DICCIONARIO__',
          child: Row(
            children: const [
              Icon(Icons.add_circle_outline, size: 18, color: Colors.green),
              SizedBox(width: 8),
              Text('Agregar al diccionario'),
            ],
          ),
        ),
      ],
    ).then((seleccion) {
      if (seleccion != null) {
        if (seleccion == '__AGREGAR_AL_DICCIONARIO__') {
          _agregarPalabraAlDiccionario(palabraIncorrecta);
        } else {
          _reemplazarPalabra(palabraIncorrecta, seleccion);
        }
      }
    });
  }

  // Reemplazar palabra en el texto
  void _reemplazarPalabra(String palabraIncorrecta, String palabraCorrecta) {
    if (textController == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('No se puede reemplazar: controlador no disponible'),
        ),
      );
      return;
    }

    final texto = textController!.text;
    final cursorPos = textController!.selection.baseOffset;

    // Buscar la palabra incorrecta cerca del cursor
    final palabraPattern = RegExp(
        r'\b' + RegExp.escape(palabraIncorrecta) + r'\b',
        caseSensitive: false);
    final matches = palabraPattern.allMatches(texto);

    if (matches.isNotEmpty) {
      // Encontrar la coincidencia más cercana al cursor
      var mejorMatch = matches.first;
      var menorDistancia = (mejorMatch.start - cursorPos).abs();

      for (final match in matches) {
        final distancia = (match.start - cursorPos).abs();
        if (distancia < menorDistancia) {
          menorDistancia = distancia;
          mejorMatch = match;
        }
      }

      // Preservar formato de mayúsculas de la palabra original
      final palabraOriginal = texto.substring(mejorMatch.start, mejorMatch.end);
      final palabraConFormato =
          _preservarFormatoMayusculas(palabraOriginal, palabraCorrecta);

      // Reemplazar la palabra
      final nuevoTexto = texto.replaceRange(
        mejorMatch.start,
        mejorMatch.end,
        palabraConFormato,
      );

      textController!.text = nuevoTexto;
      textController!.selection = TextSelection.collapsed(
        offset: mejorMatch.start + palabraConFormato.length,
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('No se encontró "$palabraIncorrecta" en el texto'),
        ),
      );
    }
  }

  bool _esPalabraCorrecta(String palabra) {
    if (!_diccionarioCargado) {
      return true; // Si no está cargado, no marcar errores
    }

    // Limpiar la palabra: solo letras y caracteres especiales del español
    final palabraLimpia = palabra
        .toLowerCase()
        .replaceAll(RegExp(r'[^a-záéíóúüñ]'), ''); // Remover puntuación

    // Ignorar palabras muy cortas, números o vacías
    if (palabraLimpia.isEmpty ||
        palabraLimpia.length < 2 ||
        RegExp(r'^\d+$').hasMatch(palabraLimpia)) {
      return true;
    }

    // ENFOQUE SIMPLE: Solo verificar si la palabra está en los diccionarios
    final resultado = _diccionarioEspanol.contains(palabraLimpia) ||
        _diccionarioPersonalizado.contains(palabraLimpia);

    // Debug desactivado para evitar spam en consola
    // if (!resultado && palabraLimpia.length > 3) {
    //   debugPrint('❌ "$palabraLimpia" NO está en diccionario');
    // }

    return resultado;
  }

  TextSpan _buildTextWithSpellCheck(String text, TextStyle? textStyle) {
    if (!_diccionarioCargado) {
      return TextSpan(text: text, style: textStyle);
    }

    final children = <InlineSpan>[];
    // Dividir el texto en palabras (incluyendo acentos) y espacios/puntuación
    final pattern =
        RegExp(r'([a-zA-ZáéíóúüñÁÉÍÓÚÜÑ]+|[^a-zA-ZáéíóúüñÁÉÍÓÚÜÑ]+)');
    final matches = pattern.allMatches(text);

    for (final match in matches) {
      final segment = match.group(0)!;

      if (RegExp(r'^[a-zA-ZáéíóúüñÁÉÍÓÚÜÑ]+$').hasMatch(segment)) {
        // Es una palabra, verificar ortografía
        final isCorrect = _esPalabraCorrecta(segment);

        if (isCorrect) {
          children.add(TextSpan(text: segment, style: textStyle));
        } else {
          // Palabra incorrecta - agregar con subrayado y reconocedor de gestos
          children.add(TextSpan(
            text: segment,
            style: (textStyle ?? const TextStyle()).copyWith(
              decoration: TextDecoration.underline,
              decorationColor: Colors.red,
              decorationStyle: TextDecorationStyle.wavy,
            ),
            recognizer: TapGestureRecognizer()
              ..onSecondaryTapDown = (details) =>
                  _mostrarMenuContextual(segment, details.globalPosition),
          ));
        }
      } else {
        // Es espacio o puntuación, agregarlo tal como está
        children.add(TextSpan(text: segment, style: textStyle));
      }
    }

    return TextSpan(children: children);
  }

  // Función helper para aplicar corrector a texto markdown manteniendo formato
  TextSpan _buildMarkdownTextWithSpellCheck(
      String markdownText, TextStyle baseStyle) {
    if (!_diccionarioCargado) {
      return TextSpan(text: markdownText, style: baseStyle);
    }

    final children = <InlineSpan>[];
    // Dividir el texto en palabras y símbolos markdown
    final pattern =
        RegExp(r'([a-zA-ZáéíóúüñÁÉÍÓÚÜÑ]+|[^a-zA-ZáéíóúüñÁÉÍÓÚÜÑ]+)');
    final matches = pattern.allMatches(markdownText);

    for (final match in matches) {
      final segment = match.group(0)!;

      if (RegExp(r'^[a-zA-ZáéíóúüñÁÉÍÓÚÜÑ]+$').hasMatch(segment)) {
        // Es una palabra, verificar ortografía
        final isCorrect = _esPalabraCorrecta(segment);

        if (isCorrect) {
          children.add(TextSpan(text: segment, style: baseStyle));
        } else {
          // Palabra incorrecta - agregar con subrayado manteniendo el formato base
          children.add(TextSpan(
            text: segment,
            style: baseStyle.copyWith(
              decoration: TextDecoration.combine([
                if (baseStyle.decoration != null) baseStyle.decoration!,
                TextDecoration.underline,
              ]),
              decorationColor: Colors.red,
              decorationStyle: TextDecorationStyle.wavy,
            ),
            recognizer: TapGestureRecognizer()
              ..onSecondaryTapDown = (details) =>
                  _mostrarMenuContextual(segment, details.globalPosition),
          ));
        }
      } else {
        // Es símbolo markdown o puntuación, agregarlo tal como está
        children.add(TextSpan(text: segment, style: baseStyle));
      }
    }

    return TextSpan(children: children);
  }

  @override
  SpecialText? createSpecialText(String flag,
      {TextStyle? textStyle,
      SpecialTextGestureTapCallback? onTap,
      int? index}) {
    return null;
  }

  @override
  TextSpan build(String data,
      {void Function(dynamic)? onTap, TextStyle? textStyle}) {
    final pattern = RegExp(
      r'(\[ref:\d+\]|\[CUADRO:\d+\]|!\[.*?\]\((.*?)\)|\*\*.*?\*\*|\*[^*]+\*|^#{1,6} .*$|\[.*?\]\(#.*?\))',
      multiLine: true,
    );
    final matches = pattern.allMatches(data);

    if (matches.isEmpty) {
      return _buildTextWithSpellCheck(data, textStyle);
    }

    final children = <InlineSpan>[];
    var lastMatchEnd = 0;

    for (final match in matches) {
      final start = match.start;
      final end = match.end;

      if (start > lastMatchEnd) {
        final textSegment = data.substring(lastMatchEnd, start);
        children.add(_buildTextWithSpellCheck(textSegment, textStyle));
      }

      final matchText = match.group(0)!;
      if (matchText.startsWith('![') && matchText.contains('](')) {
        final imageName =
            RegExp(r'!\[.*?\]\((.*?)\)').firstMatch(matchText)!.group(1)!;

        children.add(TextSpan(
          text: matchText,
          style: (textStyle ?? const TextStyle())
              .copyWith(color: Colors.green, fontStyle: FontStyle.italic),
          recognizer: TapGestureRecognizer()
            ..onTap = () {
              Navigator.push(
                context, // Use the context from the constructor
                MaterialPageRoute(
                  builder: (context) => ImagenView(
                    imageName: imageName,
                    leccionId: leccionId,
                  ),
                ),
              );
            },
        ));
      } else if (matchText.startsWith('[ref:')) {
        children.add(TextSpan(
          text: matchText,
          style: (textStyle ?? const TextStyle())
              .copyWith(color: Colors.red, fontWeight: FontWeight.bold),
        ));
      } else if (matchText.startsWith('[CUADRO:')) {
        final cuadroMatch = RegExp(r'\[CUADRO:(\d+)\]').firstMatch(matchText);
        if (cuadroMatch != null) {
          final cuadroId = cuadroMatch.group(1)!;

          children.add(TextSpan(
            text: matchText,
            style: (textStyle ?? const TextStyle()).copyWith(
              color: Colors.teal,
              fontWeight: FontWeight.bold,
              decoration: TextDecoration.underline,
            ),
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                _abrirVisorCuadro(cuadroId);
              },
          ));
        } else {
          children.add(TextSpan(text: matchText, style: textStyle));
        }
      } else if (matchText.startsWith('[') && matchText.contains('](#')) {
        // Manejar links de definiciones [texto](#enlace)
        final linkMatch = RegExp(r'\[(.*?)\]\(#(.*?)\)').firstMatch(matchText);
        if (linkMatch != null) {
          final linkText = linkMatch.group(1)!;

          children.add(TextSpan(
            text: matchText, // Mostrar el código completo del link
            style: (textStyle ?? const TextStyle()).copyWith(
              color: Colors.blue,
              decoration: TextDecoration.underline,
              fontWeight: FontWeight.w500,
            ),
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                // Abrir bottomsheet con la definición
                bs.showBottomSheet(
                    context, linkText.toLowerCase(), diccionarioCache);
              },
          ));
        } else {
          children.add(TextSpan(text: matchText, style: textStyle));
        }
      } else if (matchText.startsWith('#')) {
        // Manejar encabezados de Markdown
        final headerLevel = matchText.indexOf(' ');

        TextStyle headerStyle;
        switch (headerLevel) {
          case 1: // #
            headerStyle = (textStyle ?? const TextStyle()).copyWith(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            );
            break;
          case 2: // ##
            headerStyle = (textStyle ?? const TextStyle()).copyWith(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            );
            break;
          case 3: // ###
            headerStyle = (textStyle ?? const TextStyle()).copyWith(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            );
            break;
          case 4: // ####
            headerStyle = (textStyle ?? const TextStyle()).copyWith(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            );
            break;
          case 5: // #####
            headerStyle = (textStyle ?? const TextStyle()).copyWith(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            );
            break;
          case 6: // ######
            headerStyle = (textStyle ?? const TextStyle()).copyWith(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            );
            break;
          default:
            headerStyle = (textStyle ?? const TextStyle()).copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.black,
            );
        }

        // Aplicar corrector ortográfico a encabezados
        children.add(_buildMarkdownTextWithSpellCheck(matchText, headerStyle));
      } else if (matchText.startsWith('**') && matchText.endsWith('**')) {
        // Manejar texto en negrita CON corrector ortográfico
        final boldStyle = (textStyle ?? const TextStyle())
            .copyWith(fontWeight: FontWeight.bold);
        children.add(_buildMarkdownTextWithSpellCheck(matchText, boldStyle));
      } else if (matchText.startsWith('*') &&
          matchText.endsWith('*') &&
          !matchText.startsWith('**')) {
        // Manejar texto en cursiva CON corrector ortográfico
        final italicStyle = (textStyle ?? const TextStyle())
            .copyWith(fontStyle: FontStyle.italic);
        children.add(_buildMarkdownTextWithSpellCheck(matchText, italicStyle));
      } else {
        children.add(TextSpan(text: matchText, style: textStyle));
      }

      lastMatchEnd = end;
    }

    if (lastMatchEnd < data.length) {
      final textSegment = data.substring(lastMatchEnd, data.length);
      children.add(_buildTextWithSpellCheck(textSegment, textStyle));
    }

    return TextSpan(children: children);
  }

  // Preservar formato de mayúsculas de la palabra original
  String _preservarFormatoMayusculas(
      String palabraOriginal, String palabraCorrecta) {
    if (palabraOriginal.isEmpty || palabraCorrecta.isEmpty) {
      return palabraCorrecta;
    }

    // Caso 1: Primera letra mayúscula (Rapido → Rápido)
    if (palabraOriginal[0].toUpperCase() == palabraOriginal[0] &&
        palabraOriginal[0].toLowerCase() != palabraOriginal[0]) {
      return palabraCorrecta[0].toUpperCase() + palabraCorrecta.substring(1);
    }

    // Caso 2: Toda la palabra en mayúsculas (RAPIDO → RÁPIDO)
    if (palabraOriginal == palabraOriginal.toUpperCase()) {
      return palabraCorrecta.toUpperCase();
    }

    // Caso 3: Toda la palabra en minúsculas (rapido → rápido)
    return palabraCorrecta.toLowerCase();
  }
}

// Widget optimizado para vista previa de Markdown
class _MarkdownPreviewOptimized extends StatefulWidget {
  final TextEditingController contenidoController;
  final List<Map<String, dynamic>> referenciasBanco;
  final String Function(String, List<Map<String, dynamic>>) procesarReferencias;

  const _MarkdownPreviewOptimized({
    required this.contenidoController,
    required this.referenciasBanco,
    required this.procesarReferencias,
  });

  @override
  _MarkdownPreviewOptimizedState createState() =>
      _MarkdownPreviewOptimizedState();
}

class _MarkdownPreviewOptimizedState extends State<_MarkdownPreviewOptimized> {
  String _lastText = '';
  String _processedText = '';
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    widget.contenidoController.addListener(_onTextChanged);
    _processedText = _processText(widget.contenidoController.text);
    _lastText = widget.contenidoController.text;
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    widget.contenidoController.removeListener(_onTextChanged);
    super.dispose();
  }

  void _onTextChanged() {
    // Solo actualizar si el texto realmente cambió
    final currentText = widget.contenidoController.text;
    if (currentText == _lastText) return;

    // Debounce para evitar actualizaciones muy frecuentes
    _debounceTimer?.cancel();
    _debounceTimer = Timer(Duration(milliseconds: 500), () {
      if (mounted && currentText != _lastText) {
        setState(() {
          _processedText = _processText(currentText);
          _lastText = currentText;
        });
      }
    });
  }

  String _processText(String text) {
    return widget.procesarReferencias(text, widget.referenciasBanco);
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Markdown(
        data: _processedText,
        styleSheet: MarkdownStyleSheet.fromTheme(Theme.of(context)).copyWith(
          h1: TextStyle(
              color: Colors.black, fontSize: 24, fontWeight: FontWeight.bold),
          h2: TextStyle(
              color: Colors.black, fontSize: 20, fontWeight: FontWeight.bold),
          h3: TextStyle(
              color: Colors.black, fontSize: 18, fontWeight: FontWeight.bold),
          h4: TextStyle(
              color: Colors.black, fontSize: 16, fontWeight: FontWeight.bold),
          h5: TextStyle(
              color: Colors.black, fontSize: 14, fontWeight: FontWeight.bold),
          h6: TextStyle(
              color: Colors.black, fontSize: 12, fontWeight: FontWeight.bold),
        ),
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
      ),
    );
  }
}
