import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';

class SendNotificationPage extends StatefulWidget {
  final List<String>? userIds;

  const SendNotificationPage({super.key, this.userIds});

  @override
  SendNotificationPageState createState() => SendNotificationPageState();
}

class SendNotificationPageState extends State<SendNotificationPage> {
  final _titleController = TextEditingController();
  final _bodyController = TextEditingController();
  final Logger _logger = Logger();

  Future<void> _sendNotification() async {
    final body = _bodyController.text;
    final userIds = widget.userIds;

    if (body.isEmpty || userIds == null || userIds.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(
                  'Por favor, completa todos los campos y selecciona usuarios')),
        );
      }
      return;
    }

    try {
      final notifications = userIds
          .map((userId) => {
                'body': body,
                'user_id': userId,
              })
          .toList();

      _logger.i('Enviando notificaciones a usuarios: $userIds');

      final response = await Supabase.instance.client
          .from('notifications')
          .insert(notifications)
          .select();

      _logger.i('Respuesta de Supabase: $response');

      if (response.isEmpty) {
        throw Exception('Error al enviar las notificaciones: respuesta vacía');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Notificaciones enviadas con éxito')),
        );
        Future.delayed(Duration(seconds: 1), () {
          if (mounted) {
            Navigator.pop(context);
          }
        });
      }
      _titleController.clear();
      _bodyController.clear();
    } catch (e) {
      _logger.e('Error al enviar las notificaciones: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error al enviar las notificaciones: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Enviar Notificación'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            TextField(
              controller: _bodyController,
              decoration: InputDecoration(labelText: 'Cuerpo'),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: _sendNotification,
              child: Text('Enviar Notificación'),
            ),
          ],
        ),
      ),
    );
  }
}
