CF_R2_ACCESS_KEY=78361be5f8b9f377f389aae1bbf71cfd
CF_R2_SECRET_KEY=c7649fb867605c30313faaaaff6207e6fe39a174dd0ea521b4bcfbeac0b578ba
CF_R2_BUCKET_NAME=medbeardata
CF_R2_ENDPOINT=https://<https://ec4910ff8a539d9012be4531dbce63ff.r2.cloudflarestorage.com   
R2_ENDPOINT=https://ec4910ff8a539d9012be4531dbce63ff.r2.cloudflarestorage.com




userdownload was successfully created
Summary:
Permissions:
Allows the ability to read and list objects in specific buckets.

Buckets:
medbeardata
Use this token for authenticating against the Cloudflare API:
Token value
****************************************
Click to copy

Use the following credentials for S3 clients:
Access Key ID
07d18d3d71e6e786b69038b8894aa4<PERSON>
Click to copy

Secret Access Key
f3534f13f031ea74a78b9710ff25e27fa249d714b1595771ffb22cc275d3cf50
Click to copy

Use jurisdiction-specific endpoints for S3 clients:
Default
https://ec4910ff8a539d9012be4531dbce63ff.r2.cloudflarestorage.com
Click to copy