// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:local_auth/local_auth.dart';
import 'leccion_edit.dart';
import 'leccion_page.dart';
import 'views_chart.dart';
import 'todas_las_lecciones_page.dart';
import 'package:logging/logging.dart' show Logger;
import 'dart:convert'; // Para usar jsonDecode
import 'dart:io'; // Para trabajar con archivos y directorios
import 'package:path/path.dart' as path; // Para manejar rutas de archivos
import 'database_helper.dart'; // Asegúrate de que este archivo exista y contenga la clase DatabaseHelper

final _logger = Logger('LeccionManagerPage');

class LeccionManagerPage extends StatefulWidget {
  const LeccionManagerPage({super.key});

  @override
  LeccionManagerPageState createState() => LeccionManagerPageState();
}

class LeccionManagerPageState extends State<LeccionManagerPage> {
  String _currentLevel = 'categoria';
  int? _selectedId; // ID del elemento seleccionado en el nivel actual

  // bool _isLoading = false; // Estado de carga (eliminado porque no se usa)

  List<Map<String, dynamic>> _data = []; // Datos del nivel actual

  final LocalAuthentication _auth = LocalAuthentication();

  final ValueNotifier<List<Map<String, dynamic>>> _leccionesNotifier =
      ValueNotifier<List<Map<String, dynamic>>>([]);

  final ValueNotifier<List<Map<String, dynamic>>> _leccionesNotifier2 =
      ValueNotifier<List<Map<String, dynamic>>>([]);

  final Map<String, bool> _expandedSections = {};
  final List<int?> _navigationHistory = []; // Historial de navegación

  // Set para almacenar IDs de lecciones con borrador activo
  Set<int> _leccionesConBorrador = {};

  // Variables para búsqueda de lecciones
  final TextEditingController _searchController = TextEditingController();
  final ValueNotifier<List<Map<String, dynamic>>> _leccionesBusquedaNotifier =
      ValueNotifier<List<Map<String, dynamic>>>([]);
  Future<void> _downloadJsonFile() async {
    try {
      setState(() {
        // _isLoading = true; // Inicia la carga (eliminado porque no se usa)
      });

      final dbHelper = DatabaseHelper(); // Instancia de DatabaseHelper
      final jsonData = await dbHelper
          .fetchAllDataFromSupabase(); // Obtener datos desde Supabase

      final directory =
          Directory('C:\\FlutterProjects\\appmanager\\Galeria medicapp');
      if (!directory.existsSync()) {
        directory.createSync(recursive: true);
      }

      final filePath = path.join(directory.path, 'data.json');
      final file = File(filePath);

      await file.writeAsString(jsonEncode(jsonData), encoding: utf8);

      final fileSizeBytes = await file.length();
      final fileSizeKB = fileSizeBytes / 1024;
      _logger.info(
          'Archivo JSON guardado en $filePath con tamaño: ${fileSizeKB.toStringAsFixed(2)} KB');
    } catch (e) {
      _logger.severe('Error al descargar el archivo JSON: $e');
    } finally {
      setState(() {
        // _isLoading = false; // Finaliza la carga (eliminado porque no se usa)
      });
    }
  }

  Future<Map<String, dynamic>> _readJsonFromFile() async {
    try {
      final directory =
          Directory('C:\\FlutterProjects\\appmanager\\Galeria medicapp');
      final filePath = path.join(directory.path, 'data.json');
      final file = File(filePath);

      if (!file.existsSync()) {
        throw Exception('El archivo JSON no existe en la ruta: $filePath');
      }

      final jsonString = await file.readAsString();
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      _logger.severe('Error al leer el archivo JSON: $e');
      return {};
    }
  }

  Future<void> _fetchDataFromSupabaseAndJson() async {
    try {
      final supabaseClient = Supabase.instance.client;
      // Fetch categories ordered by 'order' ascending
      final categoriasResponse = await supabaseClient
          .from('categoria')
          .select()
          .order('order', ascending: true);

      final jsonData = await _readJsonFromFile();
      final lecciones = jsonData['lecciones'] as List<dynamic>? ?? [];
      final materias = jsonData['materias'] as List<dynamic>? ?? [];

      final categoriasConConteo = categoriasResponse.map((categoria) {
        final categoriaId = categoria['categorias'] ?? 0;
        final categoriaNombre = categoria['categoria_nombre'] ?? 'Sin nombre';

        if (categoriaNombre == 'Habilidades clínicas' ||
            categoriaNombre == 'Sobrevive a la guardia') {
          final totalLecciones = lecciones
              .where((leccion) => leccion['leccion_id2'] == categoriaId)
              .length;

          return {
            ...categoria,
            'total_lecciones': totalLecciones,
            'total_materias': 0,
          };
        } else {
          final totalMaterias = materias
              .where((materia) => materia['categoria_id'] == categoriaId)
              .length;

          return {
            ...categoria,
            'total_materias': totalMaterias,
            'total_lecciones': 0,
          };
        }
      }).toList();

      if (mounted) {
        setState(() {
          _data = categoriasConConteo;
        });
      }
    } catch (e) {
      _logger.severe('Error al obtener datos desde Supabase y JSON: $e');
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _leccionesBusquedaNotifier.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _navigationHistory.clear(); // Limpiar el historial al iniciar

    // Descargar el JSON y luego cargar los datos
    _downloadJsonFile().then((_) {
      if (mounted) {
        _fetchDataFromSupabaseAndJson(); // Cargar categorías inicialmente desde JSON
      }
    });

    _setupRealtimeSubscriptions(); // Configurar suscripciones en tiempo real
  }

  void _setupRealtimeSubscriptions() {
    final supabaseClient = Supabase.instance.client;

    supabaseClient
        .from('lecciones')
        .stream(primaryKey: ['leccion_id']).listen((event) {
      if (_currentLevel == 'lecciones') {
        _fetchLecciones();
      } else {
        _fetchDataFromSupabaseAndJson();
      }
    });
  }

  Future<void> _fetchLecciones() async {
    try {
      final supabaseClient = Supabase.instance.client;

      final lessonsResponse = await supabaseClient.from('lecciones').select(
          'leccion_id, created_at, leccion_nombre, leccion_contenido, unidades_id, leccion_id2, seccion, edited_at, order');

      final response =
          await supabaseClient.rpc('contar_impresiones_por_leccion');

      final List<dynamic> impressionsData = response as List<dynamic>;

      final Map<String, int> impressionsCountMap = {};
      for (var item in impressionsData) {
        final leccionId = item['leccion_id'].toString();
        final count = int.tryParse(item['total_impresiones'].toString()) ?? 0;
        impressionsCountMap[leccionId] = count;
      }

      final List<Map<String, dynamic>> lessonsWithCount = [];
      for (var lesson in lessonsResponse) {
        final leccionIdStr = lesson['leccion_id'].toString();
        final count = impressionsCountMap[leccionIdStr] ?? 0;
        lessonsWithCount.add({
          'leccion_id': lesson['leccion_id'] ?? 0,
          'created_at': lesson['created_at'] ?? '',
          'leccion_nombre': lesson['leccion_nombre'] ?? '',
          'leccion_contenido': lesson['leccion_contenido'] ?? '',
          'unidades_id': lesson['unidades_id'] ?? 0,
          'leccion_id2': lesson['leccion_id2'] ?? '',
          'seccion': lesson['seccion'] ?? '',
          'edited_at': lesson['edited_at'] ?? '',
          'impressions_count': count,
        });
      }

      if (mounted) {
        _leccionesNotifier.value = lessonsWithCount;
      }
    } catch (e) {
      _logger.severe('Error al obtener lecciones: $e');
    }
  }

  Future<void> _editarElemento(Map<String, dynamic> elemento) async {
    final nombreController = TextEditingController(
        text: elemento[_currentLevel == 'categoria'
            ? 'categoria_nombre'
            : _currentLevel == 'materias'
                ? 'materia_nombre'
                : _currentLevel == 'unidades'
                    ? 'unidad_nombre'
                    : 'leccion_nombre']);
    final imgController = TextEditingController(
        text: elemento[_currentLevel == 'categoria'
            ? 'categoria_img'
            : _currentLevel == 'materias'
                ? 'materia_img'
                : _currentLevel == 'unidades'
                    ? 'unidad_img'
                    : null]);

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Editar ${_currentLevel.capitalize()}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nombreController,
              decoration: const InputDecoration(labelText: 'Nombre'),
            ),
            if (_currentLevel != 'lecciones')
              TextField(
                controller: imgController,
                decoration: const InputDecoration(labelText: 'Imagen URL'),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () async {
              final tableName = _currentLevel == 'categoria'
                  ? 'categoria'
                  : _currentLevel == 'materias'
                      ? 'materias'
                      : _currentLevel == 'unidades'
                          ? 'unidades'
                          : 'lecciones';

              final idKey = _currentLevel == 'categoria'
                  ? 'categorias'
                  : _currentLevel == 'materias'
                      ? 'materia_id'
                      : _currentLevel == 'unidades'
                          ? 'unidades_id'
                          : 'leccion_id';

              final nameKey = _currentLevel == 'categoria'
                  ? 'categoria_nombre'
                  : _currentLevel == 'materias'
                      ? 'materia_nombre'
                      : _currentLevel == 'unidades'
                          ? 'unidad_nombre'
                          : 'leccion_nombre';

              final updateData = {
                nameKey: nombreController.text,
                if (_currentLevel != 'lecciones' &&
                    imgController.text.isNotEmpty)
                  _currentLevel == 'categoria'
                      ? 'categoria_img'
                      : _currentLevel == 'materias'
                          ? 'materia_img'
                          : 'unidad_img': imgController.text,
              };

              try {
                // Realizar la actualización en Supabase
                await Supabase.instance.client
                    .from(tableName)
                    .update(updateData)
                    .eq(idKey, elemento[idKey]);

                Navigator.pop(context);
                _fetchDataFromSupabaseAndJson(); // Refrescar los datos
              } catch (e) {
                _logger.severe('Error al actualizar el elemento: $e');
              }
            },
            child: const Text('Guardar'),
          ),
        ],
      ),
    );
  }

  Future<void> eliminarLeccion(int index) async {
    try {
      bool canCheckBiometrics = await _auth.canCheckBiometrics;

      if (!canCheckBiometrics) {
        if (mounted) {
          mostrarError(context);
        }
        return;
      }

      final didAuthenticate = await _auth.authenticate(
        localizedReason: 'Por favor autentícate para borrar la lección',
      );

      if (!didAuthenticate) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
                content: Text('Autenticación fallida. Inténtalo de nuevo.')),
          );
        }
        return;
      }

      final response = await Supabase.instance.client
          .from('lecciones')
          .delete()
          .match({'leccion_id': _leccionesNotifier.value[index]['leccion_id']});
      if (response.error != null) {
        _logger.severe('Error: ${response.error!.message}');
      } else {
        if (mounted) {
          _leccionesNotifier.value = List.from(_leccionesNotifier.value)
            ..removeAt(index);
        }
      }
    } catch (e) {
      _logger.severe('Error al autenticar: $e');
    }
  }

  Future<void> actualizarSeccionLeccion(
      int leccionId, String nuevaSeccion) async {
    try {
      final leccion = _leccionesNotifier.value
          .firstWhere((leccion) => leccion['leccion_id'] == leccionId);

      final response = await Supabase.instance.client.from('lecciones').upsert({
        'leccion_id': leccionId,
        'seccion': nuevaSeccion,
        'created_at': leccion['created_at'],
        'leccion_nombre': leccion['leccion_nombre'],
        'leccion_contenido': leccion['leccion_contenido'],
        'unidades_id': leccion['unidades_id'],
        'leccion_id2': leccion['leccion_id2'],
        'edited_at': leccion['edited_at'],
      });

      if (response == null) {
        _leccionesNotifier.value = [
          ..._leccionesNotifier.value
              .where((leccion) => leccion['leccion_id'] != leccionId),
          {
            'leccion_id': leccionId,
            'seccion': nuevaSeccion,
            'created_at': leccion['created_at'],
            'leccion_nombre': leccion['leccion_nombre'],
            'leccion_contenido': leccion['leccion_contenido'],
            'unidades_id': leccion['unidades_id'],
            'leccion_id2': leccion['leccion_id2'],
            'edited_at': DateTime.now(),
          },
        ];
      }
    } catch (e) {
      _logger.severe(
          'Error al actualizar la lección: $e, leccionId: $leccionId, nuevaSeccion: $nuevaSeccion');
    }
  }

  List<Map<String, dynamic>> _getLeccionesSinSeccion(
      List<Map<String, dynamic>> lecciones) {
    return lecciones
        .where((leccion) =>
            leccion['seccion'] == null || leccion['seccion'].isEmpty)
        .toList();
  }

  Map<String, List<Map<String, dynamic>>> _groupLeccionesBySection(
      List<Map<String, dynamic>> lecciones) {
    Map<String, List<Map<String, dynamic>>> grouped = {};

    for (var leccion in lecciones) {
      String? section = leccion['seccion'];
      if (section == null || section.isEmpty) continue;
      grouped.putIfAbsent(section, () => []);
      grouped[section]!.add(leccion);
    }

    return grouped;
  }

  Future<void> mostrarError(BuildContext context) {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Error'),
          content: const Text(
              'La autenticación de huellas dactilares no está disponible en este dispositivo.'),
          actions: <Widget>[
            TextButton(
              child: const Text('Cerrar'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  void _navegarSiguienteNivel(int id) {
    setState(() {
      _navigationHistory
          .add(_selectedId); // Guardar el nivel actual en el historial
      _selectedId = id; // Actualizar el ID seleccionado

      if (_currentLevel == 'categoria') {
        final categoria = _data.firstWhere(
            (elemento) => elemento['categorias'] == id,
            orElse: () => {});
        final categoriaNombre = categoria['categoria_nombre'] ?? '';

        if (categoriaNombre == 'Habilidades clínicas' ||
            categoriaNombre == 'Sobrevive a la guardia') {
          _currentLevel = 'lecciones';
          _leccionesNotifier2.value = [];
          _fetchLeccionesPorCategoria(id,
              useNotifier2: true); // Cargar lecciones
        } else {
          _currentLevel = 'materias';
          _fetchMateriasPorCategoria(id); // Cargar materias
        }
      } else if (_currentLevel == 'materias') {
        _currentLevel = 'unidades';
        _fetchUnidadesPorMateria(id); // Cargar unidades
      } else if (_currentLevel == 'unidades') {
        _currentLevel = 'lecciones';
        _fetchLeccionesPorUnidad(id); // Cargar lecciones
      }
    });
  }

  Future<void> _fetchLeccionesPorCategoria(int categoriaId,
      {bool useNotifier2 = false}) async {
    try {
      // Obtener datos desde Supabase
      final supabaseClient = Supabase.instance.client;
      final lessonsResponse = await supabaseClient
          .from('lecciones')
          .select()
          .eq('leccion_id2', categoriaId)
          .order('order', ascending: true);

      // Leer datos del archivo JSON
      final jsonData = await _readJsonFromFile();
      final lecciones = jsonData['lecciones'] as List<dynamic>? ?? [];

      // Combinar datos de Supabase con conteos del JSON
      final lessonsWithCount = lessonsResponse.map((lesson) {
        final leccionId = lesson['leccion_id'];
        final count = lecciones
            .where((leccion) => leccion['leccion_id'] == leccionId)
            .length;

        return {
          ...lesson,
          'conteo_local': count,
        };
      }).toList();

      if (mounted) {
        setState(() {
          if (useNotifier2) {
            _leccionesNotifier2.value = lessonsWithCount;
          } else {
            _leccionesNotifier.value = lessonsWithCount;
          }
        });
      }
    } catch (e) {
      _logger.severe('Error al obtener lecciones por categoría: $e');
    }
  }

  Future<void> _fetchLeccionesFilteredByUnidad(int unidadId) async {
    try {
      final supabaseClient = Supabase.instance.client;

      _logger.info(
          '🔄 Actualizando lecciones desde Supabase para unidad: $unidadId');

      final lessonsResponse = await supabaseClient
          .from('lecciones')
          .select(
              'leccion_id, created_at, leccion_nombre, leccion_contenido, unidades_id, leccion_id2, seccion, edited_at, order')
          .eq('unidades_id', unidadId) // Filtrar por unidad seleccionada
          .order('order', ascending: true);

      _logger.info(
          '📊 Obtenidas ${lessonsResponse.length} lecciones desde Supabase');

      final response =
          await supabaseClient.rpc('contar_impresiones_por_leccion');

      final List<dynamic> impressionsData = response as List<dynamic>;

      final Map<String, int> impressionsCountMap = {};
      for (var item in impressionsData) {
        final leccionId = item['leccion_id'].toString();
        final count = int.tryParse(item['total_impresiones'].toString()) ?? 0;
        impressionsCountMap[leccionId] = count;
      }

      final List<Map<String, dynamic>> lessonsWithCount = [];
      for (var lesson in lessonsResponse) {
        final leccionIdStr = lesson['leccion_id'].toString();
        final count = impressionsCountMap[leccionIdStr] ?? 0;

        // Log para verificar el contenido
        final contenido = lesson['leccion_contenido']?.toString() ?? '';
        final preview = contenido.length > 50
            ? '${contenido.substring(0, 50)}...'
            : contenido;
        _logger.info(
            '📝 Lección: ${lesson['leccion_nombre']} - Contenido: $preview');

        lessonsWithCount.add({
          ...lesson,
          'impressions_count': count,
        });
      }

      if (mounted) {
        _logger.info(
            '✅ Actualizando _leccionesNotifier con ${lessonsWithCount.length} lecciones');
        _leccionesNotifier.value = lessonsWithCount;

        // Cargar información de borradores
        await _cargarLeccionesConBorrador();
      }
    } catch (e) {
      _logger.severe('Error al obtener lecciones filtradas: $e');
    }
  }

  void _volverNivelAnterior() {
    if (mounted) {
      setState(() {
        if (_navigationHistory.isNotEmpty) {
          // Recuperar el último ID del historial
          _selectedId = _navigationHistory.removeLast();
        } else {
          _selectedId =
              null; // Si no hay historial, reiniciar el ID seleccionado
        }

        // Ajustar el nivel actual según el nivel anterior
        if (_currentLevel == 'lecciones') {
          // Determinar si venimos de unidades o de categorías especiales
          if (_navigationHistory.isNotEmpty || _selectedId != null) {
            _currentLevel = 'unidades';
            if (_selectedId != null) {
              _fetchUnidadesPorMateria(_selectedId!); // Cargar unidades
            }
          } else {
            _currentLevel = 'categoria';
            _fetchDataFromSupabaseAndJson(); // Cargar categorías
          }
        } else if (_currentLevel == 'unidades') {
          _currentLevel = 'materias';
          if (_selectedId != null) {
            _fetchMateriasPorCategoria(_selectedId!); // Cargar materias
          }
        } else if (_currentLevel == 'materias') {
          _currentLevel = 'categoria';
          _fetchDataFromSupabaseAndJson(); // Cargar categorías
        }
      });
    }
  }

  Future<void> _fetchMateriasPorCategoria(int categoriaId) async {
    try {
      final supabaseClient = Supabase.instance.client;

      // Obtener materias desde Supabase
      final materiasResponse = await supabaseClient
          .from('materias')
          .select()
          .eq('categoria_id', categoriaId)
          .order('order', ascending: true);

      // Leer datos del archivo JSON
      final jsonData = await _readJsonFromFile();
      final unidades = jsonData['unidades'] as List<dynamic>? ?? [];

      // Agregar conteo de unidades a cada materia
      final materiasConConteo = materiasResponse.map((materia) {
        final materiaId = materia['materia_id'];
        final totalUnidades = unidades
            .where((unidad) => unidad['materia_id'] == materiaId)
            .length;

        return {
          ...materia,
          'total_unidades': totalUnidades,
        };
      }).toList();

      if (mounted) {
        setState(() {
          _data = List<Map<String, dynamic>>.from(materiasConConteo);
        });
      }
    } catch (e) {
      _logger.severe('Error al obtener materias por categoría: $e');
    }
  }

  Future<void> _fetchUnidadesPorMateria(int? materiaId) async {
    if (materiaId == null) return; // Validar que el ID no sea null
    try {
      final supabaseClient = Supabase.instance.client;

      // Obtener unidades desde Supabase
      final unidadesResponse = await supabaseClient
          .from('unidades')
          .select()
          .eq('materia_id', materiaId)
          .order('order', ascending: true);

      // Leer datos del archivo JSON
      final jsonData = await _readJsonFromFile();
      final lecciones = jsonData['lecciones'] as List<dynamic>? ?? [];

      // Agregar conteo de lecciones a cada unidad
      final unidadesConConteo = unidadesResponse.map((unidad) {
        final unidadId = unidad['unidades_id'];
        final totalLecciones = lecciones
            .where((leccion) => leccion['unidades_id'] == unidadId)
            .length;

        return {
          ...unidad,
          'total_lecciones': totalLecciones,
        };
      }).toList();

      if (mounted) {
        setState(() {
          _data = List<Map<String, dynamic>>.from(unidadesConConteo);
        });
      }
    } catch (e) {
      _logger.severe('Error al obtener unidades por materia: $e');
    }
  }

  Future<void> _fetchLeccionesPorUnidad(int unidadId) async {
    try {
      final supabaseClient = Supabase.instance.client;

      // Obtener lecciones desde Supabase
      final lessonsResponse = await supabaseClient
          .from('lecciones')
          .select(
              'leccion_id, created_at, leccion_nombre, leccion_contenido, unidades_id, leccion_id2, seccion, edited_at, order')
          .eq('unidades_id', unidadId)
          .order('order', ascending: true);

      // Obtener conteo de impresiones (si es necesario)
      final response =
          await supabaseClient.rpc('contar_impresiones_por_leccion');
      final List<dynamic> impressionsData = response as List<dynamic>;

      final Map<String, int> impressionsCountMap = {};
      for (var item in impressionsData) {
        final leccionId = item['leccion_id'].toString();
        final count = int.tryParse(item['total_impresiones'].toString()) ?? 0;
        impressionsCountMap[leccionId] = count;
      }

      // Combinar datos con conteo de impresiones
      final List<Map<String, dynamic>> lessonsWithCount = [];
      for (var lesson in lessonsResponse) {
        final leccionIdStr = lesson['leccion_id'].toString();
        final count = impressionsCountMap[leccionIdStr] ?? 0;
        lessonsWithCount.add({
          ...lesson,
          'impressions_count': count,
        });
      }

      if (mounted) {
        // Actualizar el ValueNotifier correcto
        _leccionesNotifier.value = lessonsWithCount;

        // También cargar información de borradores
        await _cargarLeccionesConBorrador();
      }
    } catch (e) {
      _logger.severe('Error al obtener lecciones por unidad: $e');
    }
  }

  Future<void> _handleReorder(int oldIndex, int newIndex,
      List<Map<String, dynamic>> list, String level) async {
    final reorderedList = List<Map<String, dynamic>>.from(list);

    if (newIndex > oldIndex) {
      newIndex -= 1;
    }
    final item = reorderedList.removeAt(oldIndex);
    reorderedList.insert(newIndex, item);

    try {
      _logger.info('🚀 Iniciando actualización de orden...');
      _logger
          .info('📊 Nivel: $level, oldIndex: $oldIndex, newIndex: $newIndex');

      if (level == 'categoria') {
        // Categorías: orden secuencial global (1, 2, 3, 4...)
        for (int i = 0; i < reorderedList.length; i++) {
          final elemento = reorderedList[i];
          final elementoId = elemento['categorias'];
          final nuevoOrden = i + 1;

          _logger.info('📝 Categoría $elementoId → orden $nuevoOrden');

          await Supabase.instance.client
              .from('categoria')
              .update({'order': nuevoOrden}).eq('categorias', elementoId);
        }
      } else if (level == 'materias') {
        _logger.info('📚 Actualizando materias con orden relativo...');

        for (int i = 0; i < reorderedList.length; i++) {
          final elemento = reorderedList[i];
          final materiaId = elemento['materia_id'];
          final categoriaId = _selectedId!; 

          final nuevoOrden = (categoriaId * 1000) + (i + 1);

          _logger.info(
              '📝 Materia $materiaId → orden $nuevoOrden (Categoría $categoriaId, posición ${i + 1})');

          await Supabase.instance.client
              .from('materias')
              .update({'order': nuevoOrden}).eq('materia_id', materiaId);
        }
      } else if (level == 'unidades') {
    
        _logger.info('📁 Actualizando unidades con orden relativo...');

        for (int i = 0; i < reorderedList.length; i++) {
          final elemento = reorderedList[i];
          final unidadId = elemento['unidades_id'];
          final materiaId = _selectedId!;

  
          final nuevoOrden = (materiaId * 1000) + (i + 1);

          _logger.info(
              '📝 Unidad $unidadId → orden $nuevoOrden (Materia $materiaId, posición ${i + 1})');

          await Supabase.instance.client
              .from('unidades')
              .update({'order': nuevoOrden}).eq('unidades_id', unidadId);
        }
      }

    
      setState(() {
        _data.clear();
        _data.addAll(reorderedList);
      });

      _logger.info('✅ Orden actualizado correctamente en UI');


      await Future.delayed(Duration(milliseconds: 1000));
      await _refreshCurrentLevel();
    } catch (e) {
      _logger.severe('❌ Error general actualizando orden: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al actualizar el orden: $e'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 5),
          ),
        );
      }
    }
  }

  Future<void> _handleLeccionSublistReorder(
      int oldIndex, int newIndex, List<Map<String, dynamic>> sublist) async {
    final List<Map<String, dynamic>> reorderedSublist = List.from(sublist);
    if (newIndex > oldIndex) {
      newIndex -= 1;
    }
    final movedItem = reorderedSublist.removeAt(oldIndex);
    reorderedSublist.insert(newIndex, movedItem);

    try {
      _logger.info('🚀 Iniciando reordenamiento de lecciones...');
      _logger.info('📊 oldIndex: $oldIndex, newIndex: $newIndex');
      _logger.info('📋 Total lecciones: ${reorderedSublist.length}');


      final primeraLeccion = reorderedSublist.first;
      final seccion = primeraLeccion['seccion'];
      final unidadId = _selectedId!;

      int baseOrder;
      if (seccion == null || seccion.isEmpty) {

        baseOrder = unidadId * 1000;
        _logger.info('📝 Reordenando lecciones SIN sección (base: $baseOrder)');
      } else {
   
        baseOrder = unidadId * 1000 + _getSeccionCode(seccion);
        _logger.info(
            '📝 Reordenando lecciones de sección "$seccion" (base: $baseOrder)');
      }

      // Actualizar orden de cada lección en la sublista
      for (int i = 0; i < reorderedSublist.length; i++) {
        final leccion = reorderedSublist[i];
        final leccionId = leccion['leccion_id'];
        final nuevoOrden = baseOrder + i + 1;

        _logger.info('📝 Lección $leccionId → orden $nuevoOrden');

        await Supabase.instance.client
            .from('lecciones')
            .update({'order': nuevoOrden}).eq('leccion_id', leccionId);
      }

      _logger.info('✅ Lecciones reordenadas correctamente');
      await _refreshCurrentLevel();
    } catch (e) {
      _logger.severe('❌ Error reordenando lecciones: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al actualizar el orden: $e'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 5),
          ),
        );
      }
    }
  }

// Función auxiliar para obtener código numérico de sección
  int _getSeccionCode(String seccion) {
    // Mapear nombres de sección a códigos numéricos
    final Map<String, int> seccionCodes = {
      'Introducción': 100,
      'Conceptos Básicos': 200,
      'Procedimientos': 300,
      'Casos Clínicos': 400,
      'Evaluación': 500,
      'Recursos': 600,
      // Agregar más secciones según necesites
    };

    // Si la sección no está mapeada, usar hash del nombre
    return seccionCodes[seccion] ?? (seccion.hashCode.abs() % 900) + 100;
  }

  @override
  Widget build(BuildContext context) {
    Future<void> mostrarDialogoCrearElemento(
      BuildContext context,
      String titulo,
      String tabla,
      String nombreKey, {
      int? categoriaId,
      int? materiaId,
    }) async {
      final nombreController = TextEditingController();

      await showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(titulo),
          content: TextField(
            controller: nombreController,
            decoration: const InputDecoration(labelText: 'Nombre'),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancelar'),
            ),
            ElevatedButton(
              onPressed: () async {
                final nuevoElemento = {
                  nombreKey: nombreController.text,
                  if (categoriaId != null) 'categoria_id': categoriaId,
                  if (materiaId != null) 'materia_id': materiaId,
                };

                await Supabase.instance.client
                    .from(tabla)
                    .insert(nuevoElemento);
                Navigator.pop(context);
                _fetchDataFromSupabaseAndJson(); // Actualizar datos
              },
              child: const Text('Guardar'),
            ),
          ],
        ),
      );
    }

    List<Widget> getAppBarActions() {
      switch (_currentLevel) {
        case 'categoria':
          return [
            IconButton(
              icon: const Icon(Icons.add),
              tooltip: 'Agregar Categoría',
              onPressed: () async {
                await mostrarDialogoCrearElemento(
                  context,
                  'Nueva Categoría',
                  'categoria',
                  'categoria_nombre',
                );
              },
            ),
          ];
        case 'materias':
          return [
            IconButton(
              icon: const Icon(Icons.add),
              tooltip: 'Agregar Materia',
              onPressed: () async {
                if (_selectedId != null) {
                  await mostrarDialogoCrearElemento(
                    context,
                    'Nueva Materia',
                    'materias',
                    'materia_nombre',
                    categoriaId: _selectedId,
                  );
                }
              },
            ),
          ];
        case 'unidades':
          return [
            IconButton(
              icon: const Icon(Icons.add),
              tooltip: 'Agregar Unidad',
              onPressed: () async {
                if (_selectedId != null) {
                  await mostrarDialogoCrearElemento(
                    context,
                    'Nueva Unidad',
                    'unidades',
                    'unidad_nombre',
                    materiaId: _selectedId,
                  );
                }
              },
            ),
          ];
        case 'lecciones':
          return [
            IconButton(
              icon: const Icon(Icons.add),
              tooltip: 'Nueva Lección',
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>
                        const LeccionEditPage(leccionId: null),
                  ),
                );
              },
            ),
          ];
        default:
          return [];
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('Gestión de ${_currentLevel.capitalize()}'),
        leading: _currentLevel != 'categoria'
            ? IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: _volverNivelAnterior,
              )
            : null,
        actions: [
          // Botón para ver todas las lecciones
          IconButton(
            icon: Icon(Icons.list_alt),
            tooltip: 'Ver todas las lecciones',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => TodasLasLeccionesPage(),
                ),
              );
            },
          ),
          ...getAppBarActions(),
        ],
      ),
      body: Column(
        children: [
          // Barra de búsqueda separada
          Container(
            color: Colors.grey[100],
            padding: EdgeInsets.all(12),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.3),
                    blurRadius: 4,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Buscar lecciones por nombre...',
                  border: InputBorder.none,
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                  hintStyle: TextStyle(color: Colors.grey[500], fontSize: 16),
                  prefixIcon:
                      Icon(Icons.search, color: Colors.grey[600], size: 24),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: Icon(Icons.clear,
                              color: Colors.grey[600], size: 24),
                          onPressed: () {
                            _searchController.clear();
                            _leccionesBusquedaNotifier.value = [];
                            setState(() {}); // Para actualizar la UI
                          },
                        )
                      : null,
                ),
                style: TextStyle(color: Colors.black87, fontSize: 16),
                onChanged: (query) {
                  _buscarLecciones(query);
                  setState(() {}); // Para actualizar el botón clear
                },
              ),
            ),
          ),
          // Contenido principal
          Expanded(
            child: _searchController.text.trim().isNotEmpty
                ? _buildResultadosBusqueda()
                : (_currentLevel == 'lecciones')
                    ? RefreshIndicator(
                        onRefresh: () => _fetchLeccionesFilteredByUnidad(
                            _selectedId!), // Mantener filtro
                        child:
                            ValueListenableBuilder<List<Map<String, dynamic>>>(
                          valueListenable: _currentLevel == 'lecciones' &&
                                  _selectedId != null
                              ? (_navigationHistory.length == 1
                                  ? _leccionesNotifier2 // Desde categorías a lecciones
                                  : _leccionesNotifier) // Desde unidades a lecciones
                              : _leccionesNotifier,
                          builder: (context, lecciones, _) {
                            if (lecciones.isEmpty) {
                              return const Center(
                                  child: CircularProgressIndicator());
                            }
                            final groupedLecciones =
                                _groupLeccionesBySection(lecciones);
                            final leccionesSinSeccion =
                                _getLeccionesSinSeccion(lecciones);
                            final sections = groupedLecciones.keys.toList();

                            return SingleChildScrollView(
                              physics: AlwaysScrollableScrollPhysics(),
                              child: Column(
                                children: [
                                  if (leccionesSinSeccion.isNotEmpty)
                                    ReorderableListView.builder(
                                      key: Key(
                                          'leccionesSinSeccion_${_selectedId ?? 'default'}_${_currentLevel}_${DateTime.now().millisecondsSinceEpoch}'),
                                      shrinkWrap: true,
                                      physics:
                                          const NeverScrollableScrollPhysics(),
                                      itemCount: leccionesSinSeccion.length,
                                      onReorder: (oldIndex, newIndex) =>
                                          _handleLeccionSublistReorder(oldIndex,
                                              newIndex, leccionesSinSeccion),
                                      itemBuilder: (context, index) {
                                        final leccion =
                                            leccionesSinSeccion[index];
                                        return ListTile(
                                          key: ValueKey(
                                              'leccion_sin_seccion_${leccion['leccion_id']}_${index}_${_selectedId ?? 'default'}'),
                                          leading: const Icon(Icons.book,
                                              color: Colors.grey),
                                          title: Row(
                                            children: [
                                              Expanded(
                                                child: Text(
                                                    '${leccion['leccion_nombre']} (${leccion['impressions_count'] ?? 0} visualizaciones)'),
                                              ),
                                              if (_leccionesConBorrador
                                                  .contains(
                                                      leccion['leccion_id']))
                                                Container(
                                                  margin: const EdgeInsets.only(
                                                      left: 8),
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 6,
                                                      vertical: 2),
                                                  decoration: BoxDecoration(
                                                    color: Colors.orange,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            10),
                                                  ),
                                                  child: const Row(
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    children: [
                                                      Icon(Icons.edit,
                                                          size: 12,
                                                          color: Colors.white),
                                                      SizedBox(width: 2),
                                                      Text(
                                                        'Borrador',
                                                        style: TextStyle(
                                                          color: Colors.white,
                                                          fontSize: 10,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                            ],
                                          ),
                                          trailing: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              IconButton(
                                                onPressed: () =>
                                                    eliminarLeccion(lecciones
                                                        .indexOf(leccion)),
                                                icon: const Icon(Icons.delete),
                                              ),
                                              IconButton(
                                                onPressed: () {
                                                  Navigator.push(
                                                    context,
                                                    MaterialPageRoute(
                                                      builder: (context) =>
                                                          LeccionEditPage(
                                                        leccionContenido: leccion[
                                                            'leccion_contenido'],
                                                        leccionTitulo: leccion[
                                                            'leccion_nombre'],
                                                        leccionId: leccion[
                                                            'leccion_id'],
                                                        unidadesId: leccion[
                                                            'unidades_id'],
                                                        categoriaId: leccion[
                                                            'categoria_id'],
                                                        leccionId2: leccion[
                                                            'leccion_id2'],
                                                        createdAt: leccion[
                                                            'created_at'],
                                                        seccion:
                                                            leccion['seccion'],
                                                        editedAt: leccion[
                                                            'edited_at'],
                                                      ),
                                                    ),
                                                  );
                                                },
                                                icon: const Icon(Icons.edit),
                                              ),
                                              IconButton(
                                                icon:
                                                    const Icon(Icons.bar_chart),
                                                tooltip: 'Ver estadísticas',
                                                onPressed: () {
                                                  Navigator.push(
                                                    context,
                                                    MaterialPageRoute(
                                                      builder: (context) =>
                                                          ViewsChartPage(
                                                        leccionId: leccion[
                                                            'leccion_id'],
                                                      ),
                                                    ),
                                                  );
                                                },
                                              ),
                                              ReorderableDragStartListener(
                                                index: index,
                                                child: const Padding(
                                                  padding: EdgeInsets.all(8.0),
                                                  child:
                                                      Icon(Icons.drag_handle),
                                                ),
                                              ),
                                            ],
                                          ),
                                          onTap: () {
                                            Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder: (context) =>
                                                    LeccionPage(
                                                        titulo: leccion[
                                                            'leccion_nombre'],
                                                        contenido: leccion[
                                                            'leccion_contenido'],
                                                        leccionId: leccion[
                                                            'leccion_id'],
                                                        leccionId2: leccion[
                                                            'leccion_id2']),
                                              ),
                                            );
                                          },
                                        );
                                      },
                                    ),
                                  ExpansionPanelList(
                                    elevation: 0,
                                    dividerColor: Colors.transparent,
                                    expandedHeaderPadding: EdgeInsets.zero,
                                    expansionCallback:
                                        (int index, bool isExpanded) {
                                      setState(() {
                                        String section = sections[index];
                                        _expandedSections[section] =
                                            !(_expandedSections[section] ??
                                                false);
                                      });
                                    },
                                    children: sections.map((section) {
                                      return ExpansionPanel(
                                        canTapOnHeader: true,
                                        headerBuilder: (context, isExpanded) {
                                          return ListTile(
                                            title: Row(
                                              children: [
                                                Text(section),
                                                const SizedBox(width: 8),
                                                Text(
                                                  '${groupedLecciones[section]!.length} lecciones',
                                                  style: const TextStyle(
                                                    color: Colors.grey,
                                                    fontSize: 14,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        },
                                        body: ReorderableListView.builder(
                                          key: Key(
                                              'section_${section}_${_selectedId ?? 'default'}_${sections.indexOf(section)}_${DateTime.now().millisecondsSinceEpoch}'),
                                          shrinkWrap: true,
                                          physics:
                                              const NeverScrollableScrollPhysics(),
                                          itemCount:
                                              groupedLecciones[section]!.length,
                                          onReorder: (oldIndex, newIndex) =>
                                              _handleLeccionSublistReorder(
                                                  oldIndex,
                                                  newIndex,
                                                  groupedLecciones[section]!),
                                          itemBuilder: (context, index) {
                                            final leccion = groupedLecciones[
                                                section]![index];
                                            final leccionId =
                                                leccion['leccion_id'];
                                            final tieneBorrador =
                                                _leccionesConBorrador
                                                    .contains(leccionId);

                                            return ListTile(
                                              key: ValueKey(
                                                  'leccion_${leccionId}_section_${section}_${sections.indexOf(section)}_$index'),
                                              leading: const Icon(Icons.book,
                                                  color: Colors.grey),
                                              title: Row(
                                                children: [
                                                  Expanded(
                                                    child: Text(
                                                        '${leccion['leccion_nombre']} (${leccion['impressions_count'] ?? 0} visualizaciones)'),
                                                  ),
                                                  if (tieneBorrador)
                                                    Container(
                                                      margin:
                                                          const EdgeInsets.only(
                                                              left: 8),
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: 6,
                                                          vertical: 2),
                                                      decoration: BoxDecoration(
                                                        color: Colors.orange,
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(10),
                                                      ),
                                                      child: const Row(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        children: [
                                                          Icon(Icons.edit,
                                                              size: 12,
                                                              color:
                                                                  Colors.white),
                                                          SizedBox(width: 2),
                                                          Text(
                                                            'Borrador',
                                                            style: TextStyle(
                                                              color:
                                                                  Colors.white,
                                                              fontSize: 10,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                ],
                                              ),
                                              trailing: Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  if (tieneBorrador)
                                                    IconButton(
                                                      icon: const Icon(
                                                          Icons.delete,
                                                          color: Colors.red),
                                                      tooltip:
                                                          'Borrar Borrador',
                                                      onPressed: () {
                                                        _eliminarBorrador(
                                                            leccion);
                                                      },
                                                    ),
                                                  IconButton(
                                                    onPressed: () =>
                                                        eliminarLeccion(
                                                            _leccionesNotifier
                                                                .value
                                                                .indexOf(
                                                                    leccion)),
                                                    icon: const Icon(
                                                        Icons.delete),
                                                  ),
                                                  IconButton(
                                                    onPressed: () {
                                                      Navigator.push(
                                                        context,
                                                        MaterialPageRoute(
                                                          builder: (context) =>
                                                              LeccionEditPage(
                                                            leccionContenido:
                                                                leccion[
                                                                    'leccion_contenido'],
                                                            leccionTitulo: leccion[
                                                                'leccion_nombre'],
                                                            leccionId: leccion[
                                                                'leccion_id'],
                                                            unidadesId: leccion[
                                                                'unidades_id'],
                                                            categoriaId: leccion[
                                                                'categoria_id'],
                                                            leccionId2: leccion[
                                                                'leccion_id2'],
                                                            createdAt: leccion[
                                                                'created_at'],
                                                            seccion: leccion[
                                                                'seccion'],
                                                            editedAt: leccion[
                                                                'edited_at'],
                                                          ),
                                                        ),
                                                      );
                                                    },
                                                    icon:
                                                        const Icon(Icons.edit),
                                                  ),
                                                  IconButton(
                                                    icon: const Icon(
                                                        Icons.bar_chart),
                                                    tooltip: 'Ver estadísticas',
                                                    onPressed: () {
                                                      Navigator.push(
                                                        context,
                                                        MaterialPageRoute(
                                                          builder: (context) =>
                                                              ViewsChartPage(
                                                            leccionId: leccion[
                                                                'leccion_id'],
                                                          ),
                                                        ),
                                                      );
                                                    },
                                                  ),
                                                  ReorderableDragStartListener(
                                                    index: index,
                                                    child: const Padding(
                                                      padding:
                                                          EdgeInsets.all(8.0),
                                                      child: Icon(
                                                          Icons.drag_handle),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              onTap: () {
                                                Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                    builder: (context) => LeccionPage(
                                                        titulo: leccion[
                                                            'leccion_nombre'],
                                                        contenido: leccion[
                                                            'leccion_contenido'],
                                                        leccionId: leccion[
                                                            'leccion_id'],
                                                        leccionId2: leccion[
                                                            'leccionid2']),
                                                  ),
                                                );
                                              },
                                            );
                                          },
                                        ),
                                        isExpanded:
                                            _expandedSections[section] ?? false,
                                      );
                                    }).toList(),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _refreshCurrentLevel,
                        child: ReorderableListView.builder(
                          key: Key(
                              '${_currentLevel}_${_selectedId ?? 'root'}_${_data.length}_${DateTime.now().millisecondsSinceEpoch}'),
                          itemCount: _data.length,
                          onReorder: (oldIndex, newIndex) {
                            _handleReorder(
                                oldIndex, newIndex, _data, _currentLevel);
                          },
                          itemBuilder: (context, index) {
                            final elemento = _data[index];
                            final String idKey;
                            final String nameKey;
                            final String subtitle;
                            final IconData icon;

                            switch (_currentLevel) {
                              case 'categoria':
                                idKey = 'categorias';
                                nameKey = 'categoria_nombre';
                                subtitle = elemento['categoria_nombre'] ==
                                            'Habilidades clínicas' ||
                                        elemento['categoria_nombre'] ==
                                            'Sobrevive a la guardia'
                                    ? 'Lecciones: ${elemento['total_lecciones'] ?? 0}'
                                    : 'Materias: ${elemento['total_materias'] ?? 0}';
                                icon = Icons.category;
                                break;
                              case 'materias':
                                idKey = 'materia_id';
                                nameKey = 'materia_nombre';
                                subtitle =
                                    'Unidades: ${elemento['total_unidades'] ?? 0}';
                                icon = Icons.class_;
                                break;
                              case 'unidades':
                                idKey = 'unidades_id';
                                nameKey = 'unidad_nombre';
                                subtitle =
                                    'Lecciones: ${elemento['total_lecciones'] ?? 0}';
                                icon = Icons.folder;
                                break;
                              default:
                                return Container(
                                    key: ValueKey(
                                        'default_${index}_$_currentLevel'));
                            }

                            return ListTile(
                              key: ValueKey(
                                  '${_currentLevel}_${elemento[idKey]}_${index}_${_selectedId ?? 'root'}'),
                              leading: Icon(icon,
                                  color: Theme.of(context).primaryColor),
                              title: Text(elemento[nameKey] ?? 'Sin nombre'),
                              subtitle: Text(subtitle),
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  if (_currentLevel != 'categoria')
                                    IconButton(
                                      icon: const Icon(Icons.edit),
                                      tooltip: 'Editar',
                                      onPressed: () =>
                                          _editarElemento(elemento),
                                    ),
                                  ReorderableDragStartListener(
                                    index: index,
                                    child: const Padding(
                                      padding: EdgeInsets.all(8.0),
                                      child: Icon(Icons.drag_handle),
                                    ),
                                  ),
                                ],
                              ),
                              onTap: () {
                                final itemId = elemento[idKey];
                                if (itemId != null && itemId is int) {
                                  _navegarSiguienteNivel(itemId);
                                } else {
                                  _logger.severe(
                                      'Error: $idKey es null o no es un entero.');
                                }
                              },
                            );
                          },
                        ),
                      ),
          )
        ],
      ),
    );
  }

  // Cargar lecciones que tienen borrador activo
  Future<void> _cargarLeccionesConBorrador() async {
    try {
      final response = await Supabase.instance.client
          .from('lecciones_borradores')
          .select('leccion_id');

      final Set<int> leccionesConBorrador = {};
      for (var item in response) {
        final leccionId = item['leccion_id'] as int?;
        if (leccionId != null) {
          leccionesConBorrador.add(leccionId);
        }
      }

      setState(() {
        _leccionesConBorrador = leccionesConBorrador;
      });

      _logger.info(
          '📝 Encontradas ${leccionesConBorrador.length} lecciones con borrador activo');
      _logger.info('📝 IDs con borrador: $leccionesConBorrador');

      // DEBUG: Print simple para verificar
    } catch (e) {
      _logger.severe('Error cargando lecciones con borrador: $e');
    }
  }

  // Eliminar borrador de una lección específica
  Future<void> _eliminarBorrador(Map<String, dynamic> leccion) async {
    final leccionId = leccion['leccion_id'];
    final leccionNombre = leccion['leccion_nombre'] ?? 'Sin nombre';

    // Mostrar diálogo de confirmación
    final bool? confirmar = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.warning, color: Colors.orange),
              SizedBox(width: 8),
              Text('Eliminar Borrador'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('¿Estás seguro de que quieres eliminar el borrador de:'),
              SizedBox(height: 8),
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '"$leccionNombre"',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              SizedBox(height: 12),
              Text(
                'Esta acción no se puede deshacer. El borrador se eliminará permanentemente.',
                style: TextStyle(color: Colors.red, fontSize: 12),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text('Cancelar'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: Text('Eliminar Borrador'),
            ),
          ],
        );
      },
    );

    if (confirmar == true) {
      try {
        // Eliminar el borrador de la base de datos
        await Supabase.instance.client
            .from('lecciones_borradores')
            .delete()
            .eq('leccion_id', leccionId);

        // Actualizar la lista local
        setState(() {
          _leccionesConBorrador.remove(leccionId);
        });

        // Mostrar mensaje de éxito
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text('Borrador eliminado: "$leccionNombre"'),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        }

        _logger.info(
            '🗑️ Borrador eliminado para lección: $leccionNombre (ID: $leccionId)');
      } catch (e) {
        _logger.severe('Error eliminando borrador: $e');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.error, color: Colors.white),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text('Error al eliminar borrador: $e'),
                  ),
                ],
              ),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 5),
            ),
          );
        }
      }
    }
  }

  // Buscar lecciones por nombre
  Future<void> _buscarLecciones(String query) async {
    if (query.trim().isEmpty) {
      _leccionesBusquedaNotifier.value = [];
      return;
    }

    try {
      _logger.info('🔍 Buscando lecciones con: "$query"');

      final response = await Supabase.instance.client
          .from('lecciones')
          .select(
              'leccion_id, leccion_nombre, leccion_contenido, unidades_id, leccion_id2, seccion, edited_at, created_at')
          .ilike('leccion_nombre', '%$query%')
          .order('leccion_nombre', ascending: true)
          .limit(50); // Limitar resultados para rendimiento

      // Obtener conteo de impresiones
      final impressionsResponse =
          await Supabase.instance.client.rpc('contar_impresiones_por_leccion');

      final Map<String, int> impressionsMap = {};
      for (var item in impressionsResponse) {
        final leccionId = item['leccion_id'].toString();
        final count = int.tryParse(item['total_impresiones'].toString()) ?? 0;
        impressionsMap[leccionId] = count;
      }

      // Combinar datos con conteo de impresiones
      final List<Map<String, dynamic>> leccionesConConteo = [];
      for (var leccion in response) {
        final leccionIdStr = leccion['leccion_id'].toString();
        final count = impressionsMap[leccionIdStr] ?? 0;
        leccionesConConteo.add({
          ...leccion,
          'impressions_count': count,
        });
      }

      _leccionesBusquedaNotifier.value = leccionesConConteo;
      _logger.info('✅ Encontradas ${leccionesConConteo.length} lecciones');

      // También cargar información de borradores para los resultados
      await _cargarLeccionesConBorrador();
    } catch (e) {
      _logger.severe('Error buscando lecciones: $e');
      _leccionesBusquedaNotifier.value = [];
    }
  }

  // Método para refrescar el nivel actual
  Future<void> _refreshCurrentLevel() async {
    switch (_currentLevel) {
      case 'categoria':
        await _fetchDataFromSupabaseAndJson();
        break;
      case 'materias':
        if (_selectedId != null) {
          await _fetchMateriasPorCategoria(_selectedId!);
        }
        break;
      case 'unidades':
        if (_selectedId != null) {
          await _fetchUnidadesPorMateria(_selectedId!);
        }
        break;
      case 'lecciones':
        if (_selectedId != null) {
          await _fetchLeccionesFilteredByUnidad(_selectedId!);
          // También recargar información de borradores

          await _cargarLeccionesConBorrador();
        }
        break;
    }
  }

  // Widget para mostrar resultados de búsqueda
  Widget _buildResultadosBusqueda() {
    return ValueListenableBuilder<List<Map<String, dynamic>>>(
      valueListenable: _leccionesBusquedaNotifier,
      builder: (context, lecciones, child) {
        if (_searchController.text.trim().isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.search, size: 64, color: Colors.grey[400]),
                SizedBox(height: 16),
                Text(
                  'Escribe para buscar lecciones',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey[600],
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Busca por nombre de lección',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          );
        }

        if (lecciones.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.search_off, size: 64, color: Colors.grey[400]),
                SizedBox(height: 16),
                Text(
                  'No se encontraron lecciones',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey[600],
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Intenta con otros términos de búsqueda',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          itemCount: lecciones.length,
          itemBuilder: (context, index) {
            final leccion = lecciones[index];
            return Card(
              margin: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: ListTile(
                leading: Icon(Icons.book, color: Colors.blue),
                title: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '${leccion['leccion_nombre']} (${leccion['impressions_count'] ?? 0} visualizaciones)',
                        style: TextStyle(fontWeight: FontWeight.w500),
                      ),
                    ),
                    if (_leccionesConBorrador.contains(leccion['leccion_id']))
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            margin: EdgeInsets.only(left: 8),
                            padding: EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.orange,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.edit, size: 12, color: Colors.white),
                                SizedBox(width: 2),
                                Text(
                                  'Borrador',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(width: 4),
                          GestureDetector(
                            onTap: () => _eliminarBorrador(leccion),
                            child: Container(
                              padding: EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                color: Colors.red.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(color: Colors.red, width: 1),
                              ),
                              child: Icon(
                                Icons.close,
                                size: 14,
                                color: Colors.red,
                              ),
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
                subtitle: Text(
                  'ID: ${leccion['leccion_id']} • Unidad: ${leccion['unidades_id']}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Botón de borrar borrador (solo si tiene borrador)
                    if (_leccionesConBorrador.contains(leccion['leccion_id']))
                      IconButton(
                        icon: Icon(Icons.delete, color: Colors.red),
                        tooltip: 'Borrar Borrador',
                        onPressed: () {
                          _eliminarBorrador(leccion);
                        },
                      ),
                    IconButton(
                      icon: Icon(Icons.visibility, color: Colors.blue),
                      tooltip: 'Ver lección',
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => LeccionPage(
                              titulo: leccion['leccion_nombre'],
                              contenido: leccion['leccion_contenido'],
                              leccionId: leccion['leccion_id'],
                              leccionId2: leccion['leccionid2'],
                            ),
                          ),
                        );
                      },
                    ),
                    IconButton(
                      icon: Icon(Icons.edit, color: Colors.green),
                      tooltip: 'Editar lección',
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => LeccionEditPage(
                              leccionContenido: leccion['leccion_contenido'],
                              leccionTitulo: leccion['leccion_nombre'],
                              leccionId: leccion['leccion_id'],
                              unidadesId: leccion['unidades_id'],
                              categoriaId: null, // No disponible en búsqueda
                              leccionId2: leccion['leccion_id2'],
                              seccion: leccion['seccion'],

                              editedAt: leccion['edited_at'],
                              createdAt: leccion['created_at'],
                            ),
                          ),
                        );
                      },
                    ),
                    IconButton(
                      icon: Icon(Icons.bar_chart, color: Colors.purple),
                      tooltip: 'Ver estadísticas',
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ViewsChartPage(
                              leccionId: leccion['leccion_id'],
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => LeccionPage(
                          titulo: leccion['leccion_nombre'],
                          contenido: leccion['leccion_contenido'],
                          leccionId: leccion['leccion_id'],
                          leccionId2: leccion['leccionid2']),
                    ),
                  );
                },
              ),
            );
          },
        );
      },
    );
  }
}

extension StringExtension on String {
  String capitalize() {
    return '${this[0].toUpperCase()}${substring(1)}';
  }
}
