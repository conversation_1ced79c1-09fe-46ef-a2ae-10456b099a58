import 'package:flutter/material.dart';

class BotonesHtml extends StatelessWidget {
  final TextEditingController contenidoController;
  final Future<void> Function() abrirGaleria;

  const BotonesHtml({
    super.key,
    required this.contenidoController,
    required this.abrirGaleria,
  });

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 8.0,
      runSpacing: 4.0,
      children: <Widget>[
        IconButton(
          icon: const Icon(Icons.format_bold),
          onPressed: () {
            final currentText = contenidoController.text;
            final selection = contenidoController.selection;
            final selectedText = selection.textInside(currentText);

            if (selectedText.isNotEmpty) {
              final newText = currentText.replaceRange(
                selection.start,
                selection.end,
                '**$selectedText**',
              );

              contenidoController.text = newText;
              contenidoController.selection = selection.copyWith(
                baseOffset: selection.start + 2,
                extentOffset: selection.end + 2,
              );
            }
          },
        ),
        IconButton(
          icon: const Icon(Icons.format_italic),
          onPressed: () {
            final currentText = contenidoController.text;
            final selection = contenidoController.selection;
            final selectedText = selection.textInside(currentText);

            if (selectedText.isNotEmpty) {
              final newText = currentText.replaceRange(
                selection.start,
                selection.end,
                '*$selectedText*',
              );

              contenidoController.text = newText;
              contenidoController.selection = selection.copyWith(
                baseOffset: selection.start + 1,
                extentOffset: selection.end + 1,
              );
            }
          },
        ),
        IconButton(
          icon: const Icon(Icons.insert_link),
          onPressed: () {
            final currentText = contenidoController.text;
            final selection = contenidoController.selection;
            final selectedText = selection.textInside(currentText);

            if (selectedText.isNotEmpty) {
              final newText = currentText.replaceRange(
                selection.start,
                selection.end,
                '[$selectedText](#$selectedText)',
              );

              contenidoController.text = newText;
              contenidoController.selection = selection.copyWith(
                baseOffset: selection.start + 1,
                extentOffset: selection.start + 1 + selectedText.length,
              );
            }
          },
        ),
        IconButton(
          icon: const Icon(Icons.insert_photo),
          onPressed: abrirGaleria,
        ),
        IconButton(
          icon: const Icon(Icons.format_list_bulleted),
          onPressed: () {
            final currentText = contenidoController.text;
            final selection = contenidoController.selection;
            final selectedText = selection.textInside(currentText);

            if (selectedText.isNotEmpty) {
              final newText = currentText.replaceRange(
                selection.start,
                selection.end,
                '- $selectedText',
              );

              contenidoController.text = newText;
              contenidoController.selection = selection.copyWith(
                baseOffset: selection.start + 2,
                extentOffset: selection.end + 2,
              );
            }
          },
        ),
        IconButton(
          icon: const Icon(Icons.format_indent_increase),
          onPressed: () {
            final currentText = contenidoController.text;
            final selection = contenidoController.selection;

            final newText = currentText.replaceRange(
              selection.start,
              selection.start,
              '  ',
            );

            contenidoController.text = newText;
            contenidoController.selection = selection.copyWith(
              baseOffset: selection.start + 2,
              extentOffset: selection.start + 2,
            );
          },
        ),
        IconButton(
          icon: const Icon(Icons.title),
          onPressed: () {
            final currentText = contenidoController.text;
            final selection = contenidoController.selection;

            final newText = currentText.replaceRange(
              selection.start,
              selection.start,
              '# ',
            );

            contenidoController.text = newText;
            contenidoController.selection = selection.copyWith(
              baseOffset: selection.start + 2,
              extentOffset: selection.start + 2,
            );
          },
        ),
        IconButton(
          icon: const Icon(Icons.title),
          onPressed: () {
            final currentText = contenidoController.text;
            final selection = contenidoController.selection;

            final newText = currentText.replaceRange(
              selection.start,
              selection.start,
              '## ',
            );

            contenidoController.text = newText;
            contenidoController.selection = selection.copyWith(
              baseOffset: selection.start + 3,
              extentOffset: selection.start + 3,
            );
          },
        ),
        IconButton(
          icon: const Icon(Icons.title),
          onPressed: () {
            final currentText = contenidoController.text;
            final selection = contenidoController.selection;

            final newText = currentText.replaceRange(
              selection.start,
              selection.start,
              '### ',
            );

            contenidoController.text = newText;
            contenidoController.selection = selection.copyWith(
              baseOffset: selection.start + 4,
              extentOffset: selection.start + 4,
            );
          },
        ),
        IconButton(
          icon: const Icon(Icons.title),
          onPressed: () {
            final currentText = contenidoController.text;
            final selection = contenidoController.selection;

            final newText = currentText.replaceRange(
              selection.start,
              selection.start,
              '#### ',
            );

            contenidoController.text = newText;
            contenidoController.selection = selection.copyWith(
              baseOffset: selection.start + 5,
              extentOffset: selection.start + 5,
            );
          },
        ),
        IconButton(
          icon: const Icon(Icons.title),
          onPressed: () {
            final currentText = contenidoController.text;
            final selection = contenidoController.selection;

            final newText = currentText.replaceRange(
              selection.start,
              selection.start,
              '##### ',
            );

            contenidoController.text = newText;
            contenidoController.selection = selection.copyWith(
              baseOffset: selection.start + 6,
              extentOffset: selection.start + 6,
            );
          },
        ),
      ],
    );
  }
}
