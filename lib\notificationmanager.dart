import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';
import 'sendnotification.dart';

class NotificationManager extends StatefulWidget {
  const NotificationManager({super.key});

  @override
  NotificationManagerState createState() => NotificationManagerState();
}

class NotificationManagerState extends State<NotificationManager> {
  final Logger _logger = Logger();
  final _stream = Supabase.instance.client
      .from('notifications')
      .stream(primaryKey: ['id']).order('created_at', ascending: false);

  List<Map<String, dynamic>> _users = [];
  Set<String> _selectedUserIds = {};
  bool _loadingUsers = false;
  String? _usersError;

  @override
  void initState() {
    super.initState();
    _fetchUsers();
  }

  Future<void> _fetchUsers() async {
    setState(() {
      _loadingUsers = true;
      _usersError = null;
    });
    try {
      final List<dynamic> response = await Supabase.instance.client
          .from('profiles')
          .select()
          .order('username', ascending: true);

      setState(() {
        _users = response.cast<Map<String, dynamic>>();
        _loadingUsers = false;
      });
    } catch (e) {
      setState(() {
        _usersError = e.toString();
        _loadingUsers = false;
      });
    }
  }

  Widget _buildUserSelection() {
    if (_loadingUsers) {
      return Center(child: CircularProgressIndicator());
    }
    if (_usersError != null) {
      return Text('Error loading users: $_usersError');
    }
    return Column(
      children: [
        CheckboxListTile(
          title: Text('Seleccionar todos'),
          value: _selectedUserIds.length == _users.length && _users.isNotEmpty,
          onChanged: (bool? selected) {
            setState(() {
              if (selected == true) {
                _selectedUserIds =
                    _users.map((u) => u['user_id'] as String).toSet();
              } else {
                _selectedUserIds.clear();
              }
            });
          },
        ),
        SizedBox(
          height: 150,
          child: ListView(
            children: _users.map((user) {
              final userId = user['user_id'] as String;
              final username = user['username'] ?? '';
              return CheckboxListTile(
                title: Text(username),
                value: _selectedUserIds.contains(userId),
                onChanged: (bool? selected) {
                  setState(() {
                    if (selected == true) {
                      _selectedUserIds.add(userId);
                    } else {
                      _selectedUserIds.remove(userId);
                    }
                  });
                },
              );
            }).toList(),
          ),
        ),
        ElevatedButton(
          onPressed: _selectedUserIds.isEmpty
              ? null
              : () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => SendNotificationPage(
                          userIds: _selectedUserIds.toList()),
                    ),
                  );
                },
          child: Text('Enviar Notificación a Usuarios Seleccionados'),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Gestionar Notificaciones'),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: _buildUserSelection(),
          ),
          Expanded(
            child: StreamBuilder<List<Map<String, dynamic>>>(
              stream: _stream,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(child: CircularProgressIndicator());
                }
                if (snapshot.hasError) {
                  _logger.e(
                      'Error al obtener las notificaciones: ${snapshot.error}');
                  return Center(
                      child: Text('Error al obtener las notificaciones'));
                }
                if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return Center(child: Text('No hay notificaciones'));
                }

                final notifications = snapshot.data!;

                return SingleChildScrollView(
                  scrollDirection: Axis.vertical,
                  child: DataTable(
                    columns: const [
                      DataColumn(label: Text('Cuerpo')),
                      DataColumn(label: Text('Fecha de Envío')),
                    ],
                    rows: notifications.map((notification) {
                      final body = notification['body'] ?? '';
                      final createdAt = notification['created_at'] ?? '';
                      return DataRow(
                        cells: [
                          DataCell(Text(body)),
                          DataCell(Text(createdAt.toString())),
                        ],
                      );
                    }).toList(),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
