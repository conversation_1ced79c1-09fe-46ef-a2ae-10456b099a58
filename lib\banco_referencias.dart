import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class ReferenciasBancoPage extends StatefulWidget {
  const ReferenciasBancoPage({super.key});

  @override
  ReferenciasBancoPageState createState() => ReferenciasBancoPageState();
}

class ReferenciasBancoPageState extends State<ReferenciasBancoPage> {
  List<Map<String, dynamic>> referencias = [];
  final _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    _fetchReferencias();
  }

  Future<void> _fetchReferencias() async {
    final data = await Supabase.instance.client
        .from('referencias')
        .select()
        .order('created_at');
    setState(() {
      referencias = List<Map<String, dynamic>>.from(data);
    });
  }

  Future<void> _addReferencia(String texto) async {
    await Supabase.instance.client.from('referencias').insert({'texto': texto});
    _controller.clear();
    _fetchReferencias();
  }

  Future<void> _deleteReferencia(int id) async {
    await Supabase.instance.client.from('referencias').delete().eq('id', id);
    _fetchReferencias();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Banco de Referencias')),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _controller,
                    decoration: InputDecoration(labelText: 'Nueva referencia'),
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.add),
                  onPressed: () {
                    if (_controller.text.trim().isNotEmpty) {
                      _addReferencia(_controller.text.trim());
                    }
                  },
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: referencias.length,
              itemBuilder: (context, index) {
                final ref = referencias[index];
                return ListTile(
                  title: Text(ref['texto']),
                  trailing: IconButton(
                    icon: Icon(Icons.delete),
                    onPressed: () => _deleteReferencia(ref['id']),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
