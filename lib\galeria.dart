import 'dart:io';
import 'package:desktop_drop/desktop_drop.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:cross_file/cross_file.dart';
import 'imagen_edit.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class Galeria extends StatefulWidget {
  final int leccionId;
  const Galeria({super.key, required this.leccionId});

  @override
  GaleriaState createState() => GaleriaState();
}

class GaleriaState extends State<Galeria> {
  List<Directory> albumes = [];
  List<File> imagenes = [];
  double _zoomFactor = 1.0;
  bool mostrarAlbumes = true;
  bool isLoading = true;
  Directory? galeriaDir;
  Directory? currentAlbum;
  bool _dragging = false;
  Offset? offset;

  @override
  void initState() {
    super.initState();
    _cargarAlbumes();
  }

  Future<void> _cargarAlbumes() async {
    setState(() {
      isLoading = true;
    });

    try {
      final Directory directory =
          Directory('C:/FlutterProjects/appmanager/Galeria medicapp');
      final List<Directory> albumes =
          await compute(_listarDirectorios, directory);

      setState(() {
        galeriaDir = directory;
        this.albumes = albumes;
        isLoading = false;
      });
    } catch (e) {
      debugPrint('Error al cargar álbumes: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> _cargarImagenesDeAlbum(Directory album) async {
    setState(() {
      isLoading = true;
      currentAlbum = album;
    });

    try {
      final List<File> imagenes = await compute(_listarArchivos, album);

      setState(() {
        this.imagenes = imagenes;
        mostrarAlbumes = false;
        isLoading = false;
      });
    } catch (e) {
      debugPrint('Error al cargar imágenes del álbum: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  static List<Directory> _listarDirectorios(Directory directory) {
    return directory.listSync().whereType<Directory>().toList();
  }

  static List<File> _listarArchivos(Directory directory) {
    return directory.listSync().whereType<File>().toList();
  }

  void _aumentarZoom() {
    setState(() {
      _zoomFactor += 0.1;
    });
  }

  void _disminuirZoom() {
    setState(() {
      if (_zoomFactor > 0.1) {
        _zoomFactor -= 0.1;
      }
    });
  }

  void _volverAAlbumes() {
    setState(() {
      mostrarAlbumes = true;
      imagenes.clear();
      currentAlbum = null;
    });
  }

  Future<void> _agregarImagenes(List<XFile> files) async {
    if (currentAlbum == null) return;

    for (final file in files) {
      final newFile = File('${currentAlbum!.path}/${file.name}');
      await file.saveTo(newFile.path);

      // Get image dimensions
      final decodedImage =
          await decodeImageFromList(await newFile.readAsBytes());
      final imageWidth = decodedImage.width.toDouble();
      final imageHeight = decodedImage.height.toDouble();

      // Store image with dimensions in Supabase
      await Supabase.instance.client.from('imagenes').insert({
        'imagen_nombre': file.name,
        'width': imageWidth,
        'height': imageHeight,
        'created_at': DateTime.now().toIso8601String(),
        'edited_at': DateTime.now().toIso8601String(),
      });
    }

    _cargarImagenesDeAlbum(currentAlbum!);
  }

  Future<void> _eliminarImagen(File imagen) async {
    try {
      await imagen.delete();
      setState(() {
        imagenes.remove(imagen);
      });
    } catch (e) {
      debugPrint('Error al eliminar la imagen: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(mostrarAlbumes ? 'Álbumes' : 'Galería de Imágenes'),
        leading: !mostrarAlbumes
            ? IconButton(
                icon: Icon(Icons.arrow_back),
                onPressed: _volverAAlbumes,
              )
            : null,
        actions: [
          if (!mostrarAlbumes) ...[
            IconButton(
              icon: Icon(Icons.zoom_in),
              onPressed: _aumentarZoom,
            ),
            IconButton(
              icon: Icon(Icons.zoom_out),
              onPressed: _disminuirZoom,
            ),
          ],
        ],
      ),
      body: isLoading
          ? Center(child: CircularProgressIndicator())
          : mostrarAlbumes
              ? _construirVistaAlbumes()
              : _construirVistaImagenes(),
    );
  }

  Widget _construirVistaAlbumes() {
    return DropTarget(
      onDragDone: (detail) async {
        await _agregarImagenes(detail.files);
      },
      onDragUpdated: (details) {
        setState(() {
          offset = details.localPosition;
        });
      },
      onDragEntered: (detail) {
        setState(() {
          _dragging = true;
          offset = detail.localPosition;
        });
      },
      onDragExited: (detail) {
        setState(() {
          _dragging = false;
          offset = null;
        });
      },
      child: Container(
        color: _dragging ? Colors.blue : Colors.transparent,
        child: albumes.isEmpty
            ? Center(child: Text('No se encontraron álbumes'))
            : ListView.builder(
                itemCount: albumes.length,
                itemBuilder: (context, index) {
                  final Directory album = albumes[index];
                  return ListTile(
                    leading: Icon(Icons.folder),
                    title: Text(album.path.split('/').last),
                    onTap: () => _cargarImagenesDeAlbum(album),
                  );
                },
              ),
      ),
    );
  }

  Widget _construirVistaImagenes() {
    return DropTarget(
      onDragDone: (detail) async {
        await _agregarImagenes(detail.files);
      },
      onDragUpdated: (details) {
        setState(() {
          offset = details.localPosition;
        });
      },
      onDragEntered: (detail) {
        setState(() {
          _dragging = true;
          offset = detail.localPosition;
        });
      },
      onDragExited: (detail) {
        setState(() {
          _dragging = false;
          offset = null;
        });
      },
      child: Container(
        color: _dragging ? Colors.blue : Colors.transparent,
        child: imagenes.isEmpty
            ? Center(child: Text('No se encontraron imágenes'))
            : GridView.builder(
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  childAspectRatio: _zoomFactor,
                ),
                itemCount: imagenes.length,
                itemBuilder: (context, index) {
                  final File imagen = imagenes[index];
                  return Stack(
                    children: [
                      GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ImagenEdit(
                                imagePath: imagen.path,
                                leccionId: widget
                                    .leccionId, // Changed to match the parameter name
                              ),
                            ),
                          ).then((value) {
                            if (value != null && mounted) {
                              // Added mounted check
                              // ignore: use_build_context_synchronously
                              Navigator.pop(context, value);
                            }
                          });
                        },
                        child: Image.file(imagen),
                      ),
                      Positioned(
                        top: 0,
                        right: 0,
                        child: IconButton(
                          icon: Icon(Icons.delete, color: Colors.red),
                          onPressed: () => _eliminarImagen(imagen),
                        ),
                      ),
                    ],
                  );
                },
              ),
      ),
    );
  }
}
