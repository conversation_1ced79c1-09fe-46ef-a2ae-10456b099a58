import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'edite_definicion.dart';

class ListaDefiniciones extends StatefulWidget {
  const ListaDefiniciones({super.key});

  @override
  State<ListaDefiniciones> createState() => _ListaDefinicionesState();
}

class _ListaDefinicionesState extends State<ListaDefiniciones> {
  final _supabase = Supabase.instance.client;
  List<Map<String, dynamic>> definiciones = [];

  @override
  void initState() {
    super.initState();
    _cargarDefiniciones();
  }

  Future<void> _cargarDefiniciones() async {
    final response =
        await _supabase.from('diccionario').select().order('definicion_nombre');

    setState(() {
      definiciones = (response as List<dynamic>).cast<Map<String, dynamic>>();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Lista de Definiciones'),
      ),
      body: ListView.builder(
        itemCount: definiciones.length,
        itemBuilder: (context, index) {
          return Card(
            margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
            child: ListTile(
              title: Text(definiciones[index]['definicion_nombre'] ?? ''),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    definiciones[index]['definicion_contenido'] ?? '',
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    'Última actualización: ${definiciones[index]['created_at']}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: const Icon(Icons.edit, color: Colors.blue),
                    onPressed: () async {
                      final result = await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) =>
                              EditarDefinicion(definicion: definiciones[index]),
                        ),
                      );
                      if (result == true) {
                        _cargarDefiniciones();
                      }
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete, color: Colors.red),
                    onPressed: () async {
                      await _supabase.from('diccionario').delete().match({
                        'diccionario_id': definiciones[index]['diccionario_id']
                      });
                      _cargarDefiniciones();
                    },
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
