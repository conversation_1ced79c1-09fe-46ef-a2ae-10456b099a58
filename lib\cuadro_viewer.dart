import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class CuadroViewer extends StatefulWidget {
  final String cuadroId;

  const CuadroViewer({
    super.key,
    required this.cuadroId,
  });

  @override
  State<CuadroViewer> createState() => _CuadroViewerState();
}

class _CuadroViewerState extends State<CuadroViewer> {
  Map<String, dynamic>? cuadroData;
  bool isLoading = true;
  String? error;
  List<double> _anchosColumnas = [];
  double _fontSize = 14.0;

  @override
  void initState() {
    super.initState();
    _cargarCuadro();
  }

  Future<void> _cargarCuadro() async {
    try {
      // Convertir cuadroId a int si es necesario
      final cuadroIdInt =
          int.tryParse(widget.cuadroId) ?? int.parse(widget.cuadroId);

      final response = await Supabase.instance.client
          .from('cuadros')
          .select('titulo, json_data, created_at, updated_at')
          .eq('cuadro_id', cuadroIdInt)
          .maybeSingle();

      if (response != null) {
        // Procesar json_data para extraer anchos de columnas y color
        if (response['json_data'] != null) {
          Map<String, dynamic> contenidoData;
          try {
            contenidoData = response['json_data'] is String
                ? jsonDecode(response['json_data'] as String)
                    as Map<String, dynamic>
                : response['json_data'] as Map<String, dynamic>;
          } catch (e) {
            // print('Error parseando JSON: $e');
            contenidoData = {};
          }

          // Cargar anchos de columnas
          if (contenidoData['columnasData'] != null) {
            final columnasData = contenidoData['columnasData'] as List<dynamic>;
            _anchosColumnas = columnasData.map((col) {
              final ancho = col['ancho'];
              if (ancho is int) {
                return ancho.toDouble();
              } else if (ancho is double) {
                return ancho;
              } else {
                return 200.0; // Valor por defecto si no es numérico
              }
            }).toList();
          } else if (contenidoData['anchosColumnas'] != null) {
            _anchosColumnas =
                List<double>.from(contenidoData['anchosColumnas']);
          }

          // Cargar color si existe
          if (contenidoData['color'] != null) {}
          if (contenidoData['fontSize'] != null) {
            _fontSize = (contenidoData['fontSize'] as num).toDouble();
          }
        }

        setState(() {
          cuadroData = response;
          isLoading = false;
        });
      } else {
        setState(() {
          error = 'Cuadro no encontrado';
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        error = 'Error cargando cuadro: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(cuadroData?['titulo'] ?? 'Cuadro ${widget.cuadroId}'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.error, size: 64, color: Colors.red),
                      SizedBox(height: 16),
                      Text(error!, style: TextStyle(fontSize: 18)),
                      SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text('Volver'),
                      ),
                    ],
                  ),
                )
              : _buildCuadroContent(),
    );
  }

  List<Widget> _buildContenidoJerarquico(List<dynamic> items, {int level = 0}) {
    List<Widget> widgets = [];
    final double indent = 16.0 * level;

    for (var item in items) {
      if (item is String) {
        if (item.trim().isEmpty) continue;

        const bullets = ['•', '◦', '-'];
        final bullet =
            bullets[level < bullets.length ? level : bullets.length - 1];

        widgets.add(
          Padding(
            padding: EdgeInsets.only(
                left: indent, bottom: 2.0), // Reducido de 4.0 a 2.0
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  bullet,
                  style: TextStyle(
                    fontSize: _fontSize,
                    color: Colors.black87,
                    fontWeight: FontWeight.bold,
                    height: 1.2, // Altura de línea más compacta
                  ),
                ),
                SizedBox(width: 4.0),
                Expanded(
                  child: Text(
                    item,
                    style: TextStyle(
                      fontSize: _fontSize,
                      color: Colors.black87,
                      height: 1.2, // Altura de línea más compacta
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      } else if (item is Map<String, dynamic>) {
        final titulo = item['titulo'] as String? ?? '';
        final contenido = item['contenido'] as List<dynamic>? ?? [];

        if (titulo.isNotEmpty) {
          // Verificar si el título ya tiene una viñeta explícita
          final bool alreadyHasBullet = titulo.startsWith('•') ||
              titulo.startsWith('◦') ||
              titulo.startsWith('-') ||
              titulo.startsWith('- ');

          if (alreadyHasBullet) {
            // Es una viñeta que tiene hijos - mostrar como viñeta normal
            String cleanTitle = titulo;
            if (titulo.startsWith('• ')) {
              cleanTitle = titulo.substring(2);
            } else if (titulo.startsWith('◦ ')) {
              cleanTitle = titulo.substring(2);
            } else if (titulo.startsWith('- ')) {
              cleanTitle = titulo.substring(2);
            }

            const bullets = ['•', '◦', '-'];
            final bullet =
                bullets[level < bullets.length ? level : bullets.length - 1];

            widgets.add(
              Padding(
                padding: EdgeInsets.only(
                    left: indent, bottom: 2.0), // Reducido de 4.0 a 2.0
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      bullet,
                      style: TextStyle(
                        fontSize: _fontSize,
                        color: Colors.black87,
                        fontWeight: FontWeight.bold,
                        height: 1.2, // Altura de línea más compacta
                      ),
                    ),
                    SizedBox(width: 4.0),
                    Expanded(
                      child: Text(
                        cleanTitle,
                        style: TextStyle(
                          fontSize: _fontSize,
                          color: Colors.black87,
                          height: 1.2, // Altura de línea más compacta
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          } else {
            // Es un título normal (sin viñeta) - mostrar en negrita
            widgets.add(
              Padding(
                padding: EdgeInsets.only(
                    left: indent,
                    bottom: indent,
                    top: indent), // Reducido top de 4.0 a 2.0
                child: Text(
                  titulo,
                  style: TextStyle(
                    fontSize: _fontSize,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                    height: 1.2, // Altura de línea más compacta
                  ),
                ),
              ),
            );
          }
        }

        // Procesar contenido hijo con nivel incrementado
        widgets.addAll(_buildContenidoJerarquico(contenido, level: level + 1));
      }
    }
    return widgets;
  }

  Widget _buildCuadroContent() {
    if (cuadroData == null) return Container();

    final titulo = cuadroData!['titulo'] ?? '';
    final contenidoData = cuadroData!['json_data'] as Map<String, dynamic>?;

    // print('DEBUG - Título: $titulo');
    // print('DEBUG - Contenido Data: $contenidoData');

    if (contenidoData == null) {
      return const Center(child: Text('No hay contenido para mostrar'));
    }

    final columnas = contenidoData['columnas'] ?? 1;
    final filas = contenidoData['filas'] ?? 1;
    final colorValue = contenidoData['color'] as int?;
    final colorCuadro = colorValue != null ? Color(colorValue) : Colors.white;

    // Crear matriz de celdas
    List<List<Map<String, dynamic>>> matriz = List.generate(
      filas,
      (fila) => List.generate(
        columnas,
        (columna) => {
          'titulo': '',
          'contenido': [],
          'colorFondo': colorCuadro.toARGB32()
        },
      ),
    );

    // Procesar celdas del formato original
    // Procesar celdas del formato original
    if (contenidoData['celdas'] != null) {
      final celdasData = contenidoData['celdas'] as List<dynamic>;
      // print('DEBUG - Procesando ${celdasData.length} celdas');

      for (var celdaInfo in celdasData) {
        final celdaData = celdaInfo as Map<String, dynamic>;
        final fila = celdaData['fila'] as int;
        final columna = celdaData['columna'] as int;
        final titulo = celdaData['titulo'] as String? ?? '';
        final contenido = celdaData['contenido'] as List<dynamic>? ?? [];
        final colorFondoCelda =
            celdaData['colorFondo']; // OBTENER COLOR DE CELDA

        if (fila < filas && columna < columnas) {
          matriz[fila][columna] = {
            'titulo': titulo,
            'contenido': contenido,
            'colorFondo': colorFondoCelda ??
                colorCuadro.toARGB32(), // USAR COLOR DE CELDA O DEFAULT
          };
        }
      }
    }
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Información del cuadro
          SizedBox(height: 16),

          // Cuadro renderizado
          // Cuadro renderizado
          Container(
            width: _anchosColumnas.isNotEmpty
                ? _anchosColumnas.reduce((a, b) => a + b)
                : double.infinity,
            decoration: BoxDecoration(
              border: Border.all(
                  color: Colors.black, width: 1.0), // Borde simple negro
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Título del cuadro
                if (titulo.isNotEmpty)
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 8.0),
                    decoration: BoxDecoration(
                      color: Colors.grey[200], // Fondo gris simple
                      border: Border(
                        bottom: BorderSide(
                            color: Colors.black,
                            width: 1.0), // Solo borde inferior
                      ),
                    ),
                    child: Text(
                      titulo,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                // Tabla del cuadro
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Column(
                    children: matriz.map((fila) {
                      return IntrinsicHeight(
                        child: Row(
                          children: fila.asMap().entries.map((entry) {
                            final columna = entry.key;
                            final celda = entry.value;
                            final titulo = celda['titulo'] as String? ?? '';
                            final contenido =
                                celda['contenido'] as List<dynamic>? ?? [];

                            // Obtener color de la celda individual si existe
                            Color colorCelda =
                                Colors.white; // Color por defecto blanco
                            if (celda['colorFondo'] != null) {
                              colorCelda = Color(celda['colorFondo'] as int);
                            }

                            // Obtener ancho de columna
                            double anchoColumna = 200.0; // Ancho por defecto
                            if (_anchosColumnas.isNotEmpty &&
                                columna < _anchosColumnas.length) {
                              anchoColumna = _anchosColumnas[columna];
                            }

                            return Container(
                              width: anchoColumna,
                              padding: EdgeInsets.fromLTRB(8.0, 8.0, 8.0, 8.0),
                              decoration: BoxDecoration(
                                color:
                                    colorCelda, // Color sólido sin transparencia
                                border: Border.all(
                                    color: Colors.black,
                                    width: 1.0), // Borde negro simple
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  // Mostrar título si existe
                                  if (titulo.isNotEmpty)
                                    Padding(
                                      padding: EdgeInsets.only(bottom: 4.0),
                                      child: Text(
                                        titulo,
                                        style: TextStyle(
                                          fontSize: _fontSize,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black,
                                          height: 1.0,
                                        ),
                                      ),
                                    ),
                                  // Mostrar contenido jerárquico
                                  ..._buildContenidoJerarquico(contenido),
                                ],
                              ),
                            );
                          }).toList(),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
