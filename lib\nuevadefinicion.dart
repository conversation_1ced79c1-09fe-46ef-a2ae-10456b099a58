import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class NuevaDefinicion extends StatefulWidget {
  const NuevaDefinicion({super.key});

  @override
  State<NuevaDefinicion> createState() => _NuevaDefinicionState();
}

class _NuevaDefinicionState extends State<NuevaDefinicion> {
  final _nombreController = TextEditingController();
  final _contenidoController = TextEditingController();
  final _supabase = Supabase.instance.client;
  Future<void> _insertarDefinicion() async {
    try {
      final now = DateTime.now();
      final formattedDate =
          "${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}";

      await _supabase.from('diccionario').insert({
        'definicion_nombre': _nombreController.text,
        'definicion_contenido': _contenidoController.text,
        'created_at': formattedDate,
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Definición guardada exitosamente')),
        );
        _nombreController.clear();
        _contenidoController.clear();
      }
    } on PostgrestException catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: ${error.message}')),
        );
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error inesperado: $error')),
        );
      }
    }
  }

  @override
  void dispose() {
    _nombreController.dispose();
    _contenidoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Nueva Definición'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            TextField(
              controller: _nombreController,
              decoration: const InputDecoration(
                labelText: 'Definición Nombre',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _contenidoController,
              decoration: const InputDecoration(
                labelText: 'Definición Contenido',
                border: OutlineInputBorder(),
              ),
              maxLines: 15,
            ),
            const Spacer(),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _insertarDefinicion,
                child: const Padding(
                  padding: EdgeInsets.symmetric(vertical: 16.0),
                  child: Text('Enviar'),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
