import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'usuario_info.dart';

class MiCuentaPage extends StatelessWidget {
  const MiCuentaPage({super.key});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Map<String, dynamic>?>(
      future: _loadCurrentUserData(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (snapshot.hasError || !snapshot.hasData) {
          return Scaffold(
            appBar: AppBar(title: const Text('Mi Cuenta')),
            body: const Center(
              child: Text('Error al cargar los datos del usuario'),
            ),
          );
        }

        // Usar UsuarioInfo con los datos del usuario actual
        return UsuarioInfo(usuario: snapshot.data!);
      },
    );
  }

  Future<Map<String, dynamic>?> _loadCurrentUserData() async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) return null;

      final response = await Supabase.instance.client
          .from('profiles')
          .select('user_id, username, full_name, role, email, avatar_url, created_at, last_sign_in_at, last_activity_at, roles(role_name)')
          .eq('user_id', user.id)
          .single();

      return response;
    } catch (e) {
      return null;
    }
  }
}
