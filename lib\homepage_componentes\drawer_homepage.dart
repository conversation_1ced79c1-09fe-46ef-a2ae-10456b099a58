import 'package:flutter/material.dart';
import 'package:supabase/supabase.dart';

class DrawerHomePage extends StatelessWidget {
  final SupabaseClient client;
  const DrawerHomePage({super.key, required this.client}); // Pass key to super

  Future<void> _wait() async {
    await Future.delayed(Duration.zero);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: _wait(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          final user = client.auth.currentUser;
          final username =
              user?.userMetadata?['username'] ?? 'Default Username';
          final email = user?.email ?? 'Default Email';
          return Drawer(
            child: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color.fromARGB(255, 159, 199, 233),
                    Color.fromARGB(255, 193, 215, 224),
                    Colors.white,
                    Colors.white,
                    Colors.white,
                    Color.fromARGB(255, 206, 221, 228),
                    Colors.white,
                    Colors.white,
                  ],
                ),
              ),
              child: <PERSON><PERSON><PERSON>(
                child: ListView(
                  padding: EdgeInsets.zero,
                  children: [
                    const SizedBox(height: 80),
                    Padding(
                      padding: const EdgeInsets.only(left: 16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const CircleAvatar(
                            radius: 60, // Aumenta el tamaño del CircleAvatar
                            backgroundColor: Colors.white,
                            child: Icon(Icons.person,
                                size: 80), // Aumenta el tamaño del Icon
                          ),
                          const SizedBox(
                              height: 10), // Espacio después del CircleAvatar
                          GestureDetector(
                            onTap: () {
                              Navigator.pushNamed(context, '/miCuenta');
                            },
                            child: Text(username),
                          ),
                          const SizedBox(
                              height:
                                  10), // Espacio después del nombre de usuario
                          const SizedBox(
                              height:
                                  10), // Espacio después del nombre de usuario
                          Text(email),
                          const SizedBox(
                              height: 10), // Espacio después del correo
                          const SizedBox(
                              height: 40), // Espacio después de "Mi cuenta"
                        ],
                      ),
                    ),
                    // Aquí puedes agregar más widgets en el futuro
                  ],
                ),
              ),
            ),
          );
        } else {
          // Muestra un indicador de carga o un widget vacío mientras esperas
          return const SizedBox.shrink();
        }
      },
    );
  }
}
