import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'user_lesson_stats.dart';

class UsuarioInfo extends StatefulWidget {
  final Map<String, dynamic> usuario;

  const UsuarioInfo({super.key, required this.usuario});

  @override
  State<UsuarioInfo> createState() => _UsuarioInfoState();
}

class _UsuarioInfoState extends State<UsuarioInfo>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  Map<String, dynamic> userStats = {
    'leccionesCreadas': 0,
    'totalVisualizaciones': 0,
    'visualizacionesUnicas': 0,
    'leccionMasVista': '',
    'vistasLeccionMasVista': 0,
  };
  Map<String, dynamic> authData = {
    'created_at': null,
    'last_sign_in_at': null,
  };
  Map<String, dynamic>? _usuarioActualizado;
  bool _loadingStats = true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _animationController.forward();
    _loadUserStats();
  }

  Future<void> _loadUserStats() async {
    try {
      final userId = widget.usuario['user_id'] ?? widget.usuario['id'];

      if (userId == null) {
        return;
      }

      // Cargar datos actualizados del usuario desde la base de datos
      final usuarioResponse = await Supabase.instance.client
          .from('profiles')
          .select('created_at, last_sign_in_at, last_activity_at')
          .eq('user_id', userId)
          .single();

      _usuarioActualizado = usuarioResponse;
      print(
          'DEBUG: _usuarioActualizado[last_activity_at] = ${_usuarioActualizado?['last_activity_at']}');

      authData = {
        'created_at': widget.usuario['created_at'],
        'last_sign_in_at': widget.usuario['last_sign_in_at'],
      };

      // Obtener IDs de lecciones creadas por el usuario
      final leccionCreadorResponse = await Supabase.instance.client
          .from('leccion_creador')
          .select('leccion_id')
          .eq('creador_id', userId);

      final leccionesCreadas = leccionCreadorResponse.length;

      // Si no hay lecciones, no necesitamos continuar
      if (leccionesCreadas == 0) {
        if (mounted) {
          setState(() {
            userStats = {
              'leccionesCreadas': 0,
              'totalVisualizaciones': 0,
              'visualizacionesUnicas': 0,
              'leccionMasVista': 'Ninguna',
              'vistasLeccionMasVista': 0,
            };
            _loadingStats = false;
          });
        }
        return;
      }

      // Obtener detalles de las lecciones
      final leccionIds =
          leccionCreadorResponse.map((e) => e['leccion_id']).toList();

      final leccionesResponse = await Supabase.instance.client
          .from('lecciones')
          .select('leccion_id, leccion_nombre')
          .inFilter('leccion_id', leccionIds);

      // Obtener estadísticas de visualizaciones para cada lección
      int totalVisualizaciones = 0;
      int visualizacionesUnicas = 0;
      String leccionMasVista = '';
      int vistasLeccionMasVista = 0;

      // Obtener TODAS las visualizaciones de TODAS las lecciones del usuario en una sola consulta
      final todasLasVisualizaciones = await Supabase.instance.client
          .from('lecturas_usuario')
          .select('leccion_id, user_id')
          .inFilter('leccion_id', leccionIds);

      // Procesar las visualizaciones localmente
      final Map<int, int> vistasPorLeccion = {};
      final Map<int, Set<String>> usuariosPorLeccion = {};

      for (final vista in todasLasVisualizaciones) {
        final leccionId = vista['leccion_id'] as int;
        final userId = vista['user_id'] as String;

        // Contar vistas totales
        vistasPorLeccion[leccionId] = (vistasPorLeccion[leccionId] ?? 0) + 1;

        // Contar usuarios únicos
        usuariosPorLeccion[leccionId] ??= <String>{};
        usuariosPorLeccion[leccionId]!.add(userId);
      }

      // Calcular estadísticas
      for (final leccion in leccionesResponse) {
        final leccionId = leccion['leccion_id'] as int;
        final leccionNombre = leccion['leccion_nombre'] as String;

        final vistas = vistasPorLeccion[leccionId] ?? 0;
        final usuariosUnicos = usuariosPorLeccion[leccionId]?.length ?? 0;

        totalVisualizaciones += vistas;
        visualizacionesUnicas += usuariosUnicos;

        if (vistas > vistasLeccionMasVista) {
          vistasLeccionMasVista = vistas;
          leccionMasVista = leccionNombre;
        }
      }

      if (mounted) {
        setState(() {
          userStats = {
            'leccionesCreadas': leccionesCreadas,
            'totalVisualizaciones': totalVisualizaciones,
            'visualizacionesUnicas': visualizacionesUnicas,
            'leccionMasVista':
                leccionMasVista.isEmpty ? 'Ninguna' : leccionMasVista,
            'vistasLeccionMasVista': vistasLeccionMasVista,
          };
          _loadingStats = false;
          // Los datos de _usuarioActualizado ya están cargados y se refrescarán
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _loadingStats = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: CustomScrollView(
        slivers: [
          // App Bar con gradiente
          SliverAppBar(
            expandedHeight: 200,
            floating: false,
            pinned: true,
            backgroundColor: Colors.transparent,
            flexibleSpace: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    colorScheme.primary,
                    colorScheme.primary.withValues(alpha: 0.8),
                  ],
                ),
              ),
              child: FlexibleSpaceBar(
                title: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Text(
                    widget.usuario['full_name'] ??
                        widget.usuario['username'] ??
                        'Perfil de Usuario',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                centerTitle: true,
                background: SlideTransition(
                  position: _slideAnimation,
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            colorScheme.primary,
                            colorScheme.primary.withValues(alpha: 0.8),
                          ],
                        ),
                      ),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const SizedBox(height: 30),
                            _buildAvatar(),
                            const SizedBox(height: 8),
                            Text(
                              widget.usuario['full_name'] ??
                                  widget.usuario['username'] ??
                                  'Usuario',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 2),
                            Text(
                              widget.usuario['roles']?['role_name'] ??
                                  'Usuario',
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.9),
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),

          // Contenido principal
          SliverToBoxAdapter(
            child: SlideTransition(
              position: _slideAnimation,
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildSectionTitle('Información Personal', Icons.person),
                      const SizedBox(height: 16),
                      _buildInfoCard([
                        _buildInfoItem(
                          icon: Icons.person_outline,
                          title: 'Nombre Completo',
                          value:
                              widget.usuario['full_name'] ?? 'No especificado',
                          color: Colors.blue,
                        ),
                        _buildInfoItem(
                          icon: Icons.account_circle_outlined,
                          title: 'Nombre de Usuario',
                          value:
                              widget.usuario['username'] ?? 'No especificado',
                          color: Colors.green,
                        ),
                        _buildInfoItem(
                          icon: Icons.email_outlined,
                          title: 'Correo Electrónico',
                          value: widget.usuario['email'] ?? 'No especificado',
                          color: Colors.orange,
                        ),
                        _buildInfoItem(
                          icon: Icons.admin_panel_settings_outlined,
                          title: 'Rol',
                          value: widget.usuario['roles']?['role_name'] ??
                              'No especificado',
                          color: Colors.purple,
                        ),
                      ]),
                      const SizedBox(height: 24),
                      _buildSectionTitle(
                          'Estadísticas de Lecciones', Icons.analytics),
                      const SizedBox(height: 16),
                      _buildStatsCards(),
                      const SizedBox(height: 16),
                      Center(
                        child: TextButton(
                          onPressed: () {
                            final userId = widget.usuario['user_id'] ??
                                widget.usuario['id'];
                            if (userId != null) {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) =>
                                      UserLessonStatsPage(userId: userId),
                                ),
                              );
                            }
                          },
                          child: const Text(
                            'Ver todas las estadísticas',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAvatar() {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.white,
          width: 4,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: CircleAvatar(
        radius: 40,
        backgroundColor: Colors.white,
        backgroundImage: widget.usuario['avatar_url'] != null
            ? NetworkImage(widget.usuario['avatar_url'])
            : null,
        child: widget.usuario['avatar_url'] == null
            ? Icon(
                Icons.person,
                size: 40,
                color: Colors.grey[600],
              )
            : null,
      ),
    );
  }

  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: Theme.of(context).colorScheme.primary,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
      ],
    );
  }

  Widget _buildInfoCard(List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: children.asMap().entries.map((entry) {
          final index = entry.key;
          final child = entry.value;
          return Column(
            children: [
              child,
              if (index < children.length - 1)
                Divider(
                  height: 1,
                  color: Colors.grey[200],
                ),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCards() {
    return Column(
      children: [
        // Primera tarjeta: Información básica del usuario
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    title: 'Miembro desde',
                    value: _formatDate(authData['created_at']),
                    icon: Icons.calendar_today,
                    color: Colors.teal,
                  ),
                ),
                Container(
                  width: 1,
                  height: 60,
                  color: Colors.grey[200],
                ),
                Expanded(
                  child: _buildStatItem(
                    title: 'Última actividad',
                    value: _formatDate(
                        _usuarioActualizado?['last_activity_at'] ??
                            authData['last_sign_in_at']),
                    icon: Icons.access_time,
                    color: Colors.indigo,
                  ),
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Segunda tarjeta: Estadísticas de lecciones
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: _loadingStats
                ? const Center(
                    child: Padding(
                      padding: EdgeInsets.all(20),
                      child: CircularProgressIndicator(),
                    ),
                  )
                : Column(
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: _buildStatItem(
                              title: 'Lecciones creadas',
                              value: '${userStats['leccionesCreadas']}',
                              icon: Icons.school,
                              color: Colors.blue,
                            ),
                          ),
                          Container(
                            width: 1,
                            height: 60,
                            color: Colors.grey[200],
                          ),
                          Expanded(
                            child: _buildStatItem(
                              title: 'Total de vistas',
                              value: _formatNumber(
                                  userStats['totalVisualizaciones']),
                              icon: Icons.visibility,
                              color: Colors.green,
                            ),
                          ),
                        ],
                      ),
                      if (userStats['leccionesCreadas'] > 0) ...[
                        const SizedBox(height: 16),
                        Container(
                          width: double.infinity,
                          height: 1,
                          color: Colors.grey[200],
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: _buildStatItem(
                                title: 'Vistas únicas',
                                value: _formatNumber(
                                    userStats['visualizacionesUnicas']),
                                icon: Icons.people,
                                color: Colors.orange,
                              ),
                            ),
                            Container(
                              width: 1,
                              height: 60,
                              color: Colors.grey[200],
                            ),
                            Expanded(
                              child: _buildStatItem(
                                title: 'Lección más vista',
                                value: userStats['leccionMasVista'],
                                subtitle:
                                    '${userStats['vistasLeccionMasVista']} vistas',
                                icon: Icons.star,
                                color: Colors.purple,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem({
    required String title,
    required String value,
    String? subtitle,
    required IconData icon,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 28,
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        if (subtitle != null) ...[
          const SizedBox(height: 2),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w400,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }

  String _formatNumber(int number) {
    if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    } else {
      return number.toString();
    }
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'N/A';

    try {
      final date = DateTime.parse(dateString).toLocal();
      final now = DateTime.now();
      final difference = now.difference(date);

      print('DEBUG: dateString = $dateString');
      print('DEBUG: date.toLocal() = $date');
      print('DEBUG: now = $now');
      print('DEBUG: difference.inHours = ${difference.inHours}');

      if (difference.inDays > 30) {
        return '${date.day}/${date.month}/${date.year}';
      } else if (difference.inDays > 0) {
        return 'Hace ${difference.inDays} días';
      } else if (difference.inHours > 0) {
        return 'Hace ${difference.inHours} horas';
      } else {
        return 'Hace ${difference.inMinutes} minutos';
      }
    } catch (e) {
      return 'N/A';
    }
  }
}
